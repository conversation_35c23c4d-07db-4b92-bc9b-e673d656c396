import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../data/models/media_item.dart';
import '../../../data/models/media_ui_model.dart';
import '../../../data/repository/media_repository.dart';
import '../../../di/components/service_locator.dart';

part 'media_providers.g.dart';

final MediaRepository _mediaRepository = getIt<MediaRepository>();

/// Provider for all media items
@riverpod
Future<List<MediaUiModel>> mediaList(Ref ref) async {
  try {
    final List<MediaItem> items = await _mediaRepository.getAllMediaItems();
    return items
        .map((MediaItem item) => MediaUiModel.fromDomain(item))
        .toList();
  } catch (e) {
    debugPrint('Error loading media items: $e');
    return <MediaUiModel>[];
  }
}

/// Provider to get a specific media item by ID
@riverpod
Future<MediaUiModel?> mediaItem(Ref ref, String itemId) async {
  try {
    // Get all media items and find the one with matching ID
    final List<MediaUiModel> allItems =
        await ref.watch(mediaListProvider.future);
    return allItems.firstWhere(
      (MediaUiModel item) => item.id == itemId,
      orElse: () => throw Exception('Media item not found: $itemId'),
    );
  } catch (e) {
    debugPrint('Error fetching media item $itemId: $e');
    return null;
  }
}
