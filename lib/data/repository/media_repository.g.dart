// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'media_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(mediaRepository)
const mediaRepositoryProvider = MediaRepositoryProvider._();

final class MediaRepositoryProvider
    extends $FunctionalProvider<MediaRepository, MediaRepository>
    with $Provider<MediaRepository> {
  const MediaRepositoryProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'mediaRepositoryProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$mediaRepositoryHash();

  @$internal
  @override
  $ProviderElement<MediaRepository> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  MediaRepository create(Ref ref) {
    return mediaRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(MediaRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<MediaRepository>(value),
    );
  }
}

String _$mediaRepositoryHash() => r'2fda779c232e471156d036a7558ad656f3ef864f';

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
