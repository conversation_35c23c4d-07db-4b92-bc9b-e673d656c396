import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../data/models/media_ui_model.dart';
import '../../../data/models/user_data_models.dart';
import '../../../routing/app_router.dart';
import '../../../utils/context_extensions.dart';
import '../../user_data/providers/media_providers.dart';
import '../../user_data/providers/user_data_providers.dart';

class FavoritesScreen extends ConsumerWidget {
  const FavoritesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Add auto-refresh to ensure the list updates when items are removed
    final AsyncValue<List<FavoriteItem>> favoritesAsync =
        ref.watch(favoritesNotifierProvider);

    // Listen for changes to the favorites list
    ref.listen<AsyncValue<List<FavoriteItem>>>(
      favoritesNotifierProvider,
      (_, __) {
        // This will trigger when the favorites list changes
      },
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('المفضلة'),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.delete_outline),
            onPressed: () => _confirmClearFavorites(context, ref),
            tooltip: 'حذف جميع المفضلة',
          ),
        ],
      ),
      backgroundColor: context.colorScheme.surface,
      body: favoritesAsync.when(
        data: (List<FavoriteItem> favorites) {
          if (favorites.isEmpty) {
            return _buildEmptyState(context);
          }
          return _buildFavoritesList(context, ref, favorites);
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (Object error, StackTrace stackTrace) => Center(
          child: Text('حدث خطأ: $error'),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Icons.favorite_border,
            size: 64,
            color: context.colorScheme.secondary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد عناصر في المفضلة',
            style: context.textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'يمكنك إضافة العناصر إلى المفضلة بالضغط على أيقونة القلب',
            style: context.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFavoritesList(
    BuildContext context,
    WidgetRef ref,
    List<FavoriteItem> favorites,
  ) {
    return ListView.builder(
      itemCount: favorites.length,
      padding: const EdgeInsets.all(16),
      itemBuilder: (BuildContext context, int index) {
        final FavoriteItem favorite = favorites[index];

        // Get the media item details using the itemId
        final AsyncValue<MediaUiModel?> mediaItemAsync =
            ref.watch(mediaItemProvider(favorite.itemId));

        return mediaItemAsync.when(
          data: (MediaUiModel? mediaItem) {
            if (mediaItem == null) {
              return _buildUnavailableItem(context, favorite, ref);
            }
            return _buildFavoriteItem(context, ref, favorite, mediaItem);
          },
          loading: () => const Card(
            margin: EdgeInsets.only(bottom: 16),
            child: ListTile(
              title: Text('جاري التحميل...'),
              leading: CircularProgressIndicator(),
            ),
          ),
          error: (Object error, StackTrace stackTrace) => const Card(
            margin: EdgeInsets.only(bottom: 16),
            child: ListTile(
              title: Text('عنصر غير متوفر'),
              subtitle: Text('تعذر تحميل بيانات العنصر'),
              leading: Icon(Icons.error_outline),
            ),
          ),
        );
      },
    );
  }

  Widget _buildUnavailableItem(
    BuildContext context,
    FavoriteItem favorite,
    WidgetRef ref,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ListTile(
        title: Text('عنصر غير متوفر (${favorite.itemId})'),
        subtitle: Text(
          'تاريخ الإضافة: ${_formatDate(favorite.createdAt)}',
        ),
        leading: const Icon(Icons.help_outline),
        trailing: IconButton(
          icon: const Icon(Icons.delete_outline),
          onPressed: () => _removeFavorite(context, favorite, ref),
          tooltip: 'إزالة من المفضلة',
        ),
        // Navigate to item details page
        onTap: () => _navigateToItemDetails(context, favorite.itemId),
      ),
    );
  }

  Widget _buildFavoriteItem(
    BuildContext context,
    WidgetRef ref,
    FavoriteItem favorite,
    MediaUiModel mediaItem,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ListTile(
        title: Text(mediaItem.title),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(mediaItem.category),
            Text(
              'تاريخ الإضافة: ${_formatDate(favorite.createdAt)}',
              style: context.textTheme.bodySmall,
            ),
          ],
        ),
        isThreeLine: true,
        leading: mediaItem.thumbnailUrl != null
            ? ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: Image.network(
                  mediaItem.thumbnailUrl!,
                  width: 56,
                  height: 56,
                  fit: BoxFit.cover,
                  errorBuilder: (BuildContext context, Object error,
                      StackTrace? stackTrace) {
                    return const Icon(Icons.broken_image, size: 56);
                  },
                ),
              )
            : Icon(
                _getIconForType(mediaItem.type),
                size: 56,
              ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            IconButton(
              icon: const Icon(Icons.play_arrow),
              onPressed: () => _playMedia(context, mediaItem),
              tooltip: 'تشغيل',
            ),
            IconButton(
              icon: const Icon(Icons.favorite, color: Colors.red),
              onPressed: () => _removeFavorite(context, favorite, ref),
              tooltip: 'إزالة من المفضلة',
            ),
          ],
        ),
        onTap: () => _playMedia(context, mediaItem),
      ),
    );
  }

  void _playMedia(BuildContext context, MediaUiModel mediaItem) {
    // Always navigate to the MediaPlayerScreen regardless of media type
    context.push('${SGRoute.mediaPlayer.route}/${mediaItem.id}');
  }

  // Navigate to item details page
  void _navigateToItemDetails(BuildContext context, String itemId) {
    // Always navigate to the MediaPlayerScreen
    context.push('${SGRoute.mediaPlayer.route}/$itemId');
  }

  Future<void> _removeFavorite(
      BuildContext context, FavoriteItem favorite, WidgetRef ref) async {
    // Toggle favorite (which will remove it since it's already favorited)
    final bool result =
        await ref.read(favoritesNotifierProvider.notifier).toggleFavorite(
              itemId: favorite.itemId,
              type: favorite.type,
            );

    // Force refresh the favorites list
    ref.invalidate(favoritesNotifierProvider);

    // Also invalidate the specific media item to ensure it's refreshed
    ref.invalidate(mediaItemProvider(favorite.itemId));

    // Show a snackbar to confirm the action
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تمت إزالة العنصر من المفضلة')),
      );
    }
  }

  Future<void> _confirmClearFavorites(
      BuildContext context, WidgetRef ref) async {
    final bool confirm = await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('حذف المفضلة'),
              content: const Text('هل أنت متأكد أنك تريد حذف جميع المفضلة؟'),
              actions: <Widget>[
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('إلغاء'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  style: TextButton.styleFrom(foregroundColor: Colors.red),
                  child: const Text('حذف'),
                ),
              ],
            );
          },
        ) ??
        false;

    if (confirm && context.mounted) {
      await ref.read(favoritesNotifierProvider.notifier).clearFavorites();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حذف جميع المفضلة بنجاح')),
        );
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day}';
  }

  IconData _getIconForType(String type) {
    switch (type.toLowerCase()) {
      case 'video':
        return Icons.video_library;
      case 'audio':
        return Icons.audiotrack;
      case 'pdf':
      case 'document':
        return Icons.insert_drive_file;
      case 'text':
        return Icons.article;
      case 'tweet':
        return Icons.chat;
      default:
        return Icons.insert_drive_file;
    }
  }
}
