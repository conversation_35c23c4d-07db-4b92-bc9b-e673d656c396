import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../data/models/media_ui_model.dart';
import '../../../data/models/user_data_models.dart';
import '../../../routing/app_router.dart';
import '../../../utils/context_extensions.dart';
import '../../../utils/icons_changer.dart';
import '../../user_data/providers/media_providers.dart';
import '../../user_data/providers/user_data_providers.dart';

class ProgressScreen extends ConsumerWidget {
  const ProgressScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<List<ProgressItem>> progressAsync =
        ref.watch(progressNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('تقدم المشاهدة'),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.delete_outline),
            onPressed: () => _confirmClearProgress(context, ref),
            tooltip: 'حذف تقدم المشاهدة',
          ),
        ],
      ),
      backgroundColor: context.colorScheme.surface,
      body: progressAsync.when(
        data: (List<ProgressItem> progressItems) {
          if (progressItems.isEmpty) {
            return _buildEmptyState(context);
          }
          return _buildProgressList(context, ref, progressItems);
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (Object error, StackTrace stackTrace) => Center(
          child: Text('حدث خطأ: $error'),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Icons.play_circle_outline,
            size: 64,
            color: context.colorScheme.secondary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'لا يوجد تقدم مشاهدة',
            style: context.textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'سيظهر هنا تقدم مشاهدتك للفيديوهات والصوتيات',
            style: context.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProgressList(
    BuildContext context,
    WidgetRef ref,
    List<ProgressItem> progressItems,
  ) {
    // Sort progress by last updated (newest first)
    final List<ProgressItem> sortedProgress =
        List<ProgressItem>.from(progressItems)
          ..sort((ProgressItem a, ProgressItem b) =>
              b.lastUpdated.compareTo(a.lastUpdated));

    return ListView.builder(
      itemCount: sortedProgress.length,
      padding: const EdgeInsets.all(16),
      itemBuilder: (BuildContext context, int index) {
        final ProgressItem progressItem = sortedProgress[index];

        // Get the media item details using the itemId
        final AsyncValue<MediaUiModel?> mediaItemAsync =
            ref.watch(mediaItemProvider(progressItem.itemId));

        return mediaItemAsync.when(
          data: (MediaUiModel? mediaItem) {
            if (mediaItem == null) {
              return _buildUnavailableItem(context, progressItem);
            }
            return _buildProgressItem(context, ref, progressItem, mediaItem);
          },
          loading: () => const Card(
            margin: EdgeInsets.only(bottom: 16),
            child: ListTile(
              title: Text('جاري التحميل...'),
              leading: CircularProgressIndicator(),
            ),
          ),
          error: (Object error, StackTrace stackTrace) => const Card(
            margin: EdgeInsets.only(bottom: 16),
            child: ListTile(
              title: Text('عنصر غير متوفر'),
              subtitle: Text('تعذر تحميل بيانات العنصر'),
              leading: Icon(Icons.error_outline),
            ),
          ),
        );
      },
    );
  }

  Widget _buildUnavailableItem(
      BuildContext context, ProgressItem progressItem) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ListTile(
        title: Text('عنصر غير متوفر (${progressItem.itemId})'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text('الموضع: ${_formatDuration(progressItem.positionSeconds)}'),
            Text(
              'آخر تحديث: ${_formatDateTime(progressItem.lastUpdated)}',
              style: context.textTheme.bodySmall,
            ),
          ],
        ),
        isThreeLine: true,
        leading: const Icon(Icons.help_outline),
        // Navigate to item details page
        onTap: () => _navigateToItemDetails(context, progressItem.itemId),
      ),
    );
  }

  Widget _buildProgressItem(
    BuildContext context,
    WidgetRef ref,
    ProgressItem progressItem,
    MediaUiModel mediaItem,
  ) {
    // Calculate progress percentage if duration is available
    double? progressPercentage;
    double? duration;

    // Try to get duration from metadata
    if (mediaItem.metadata != null &&
        mediaItem.metadata!.additionalInfo != null &&
        mediaItem.metadata!.additionalInfo!.containsKey('duration')) {
      duration = mediaItem.metadata!.additionalInfo!['duration'] as double?;
    }

    if (duration != null && duration > 0) {
      progressPercentage = progressItem.positionSeconds / duration;
      // Clamp to valid range
      progressPercentage = progressPercentage.clamp(0.0, 1.0);
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          ListTile(
            title: Text(mediaItem.title),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                    '${mediaItem.category} - ${_formatDuration(progressItem.positionSeconds)}'),
                Text(
                  'آخر تحديث: ${_formatDateTime(progressItem.lastUpdated)}',
                  style: context.textTheme.bodySmall,
                ),
              ],
            ),
            isThreeLine: true,
            leading: mediaItem.thumbnailUrl != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: Image.network(
                      mediaItem.thumbnailUrl!,
                      width: 56,
                      height: 56,
                      fit: BoxFit.cover,
                      errorBuilder: (BuildContext context, Object error,
                          StackTrace? stackTrace) {
                        return const Icon(Icons.broken_image, size: 56);
                      },
                    ),
                  )
                : Icon(
                    getIconForType(mediaItem.type),
                    size: 56,
                  ),
            trailing: IconButton(
              icon: const Icon(Icons.play_arrow),
              onPressed: () => _playMedia(context, mediaItem),
              tooltip: 'استئناف',
            ),
            onTap: () => _playMedia(context, mediaItem),
          ),
          if (progressPercentage != null) ...<Widget>[
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  LinearProgressIndicator(
                    value: progressPercentage,
                    backgroundColor:
                        context.colorScheme.surfaceContainerHighest,
                    valueColor: AlwaysStoppedAnimation<Color>(
                        context.colorScheme.primary),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      Text(
                        _formatDuration(progressItem.positionSeconds),
                        style: context.textTheme.bodySmall,
                      ),
                      Text(
                        duration != null ? _formatDuration(duration) : '--:--',
                        style: context.textTheme.bodySmall,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _playMedia(BuildContext context, MediaUiModel mediaItem) {
    // Always navigate to the MediaPlayerScreen regardless of media type
    context.push('${SGRoute.mediaPlayer.route}/${mediaItem.id}');
  }

  // Navigate to item details page
  void _navigateToItemDetails(BuildContext context, String itemId) {
    // Always navigate to the MediaPlayerScreen
    context.push('${SGRoute.mediaPlayer.route}/$itemId');
  }

  Future<void> _confirmClearProgress(
      BuildContext context, WidgetRef ref) async {
    final bool confirm = await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('حذف تقدم المشاهدة'),
              content: const Text('هل أنت متأكد أنك تريد حذف تقدم المشاهدة؟'),
              actions: <Widget>[
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('إلغاء'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  style: TextButton.styleFrom(foregroundColor: Colors.red),
                  child: const Text('حذف'),
                ),
              ],
            );
          },
        ) ??
        false;

    if (confirm && context.mounted) {
      await ref.read(progressNotifierProvider.notifier).clearProgress();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حذف تقدم المشاهدة بنجاح')),
        );
      }
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month}/${dateTime.day} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _formatDuration(double seconds) {
    final Duration duration = Duration(seconds: seconds.round());
    final int hours = duration.inHours;
    final int minutes = duration.inMinutes.remainder(60);
    final int secs = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '$hours:${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    }
  }
}
