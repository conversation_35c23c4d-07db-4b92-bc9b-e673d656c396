import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../data/enums/media_file_type.dart';
import '../../../data/models/media_item.dart';
import '../../../data/models/media_items/media_player_controller.dart';
import '../providers/pdf_reader_provider.dart';

/// Interface for document download functionality
abstract class DocumentDownloader {
  Future<void> downloadMedia({String? specificUrl, String? fileType});

  /// Helper method to download media with a MediaFileType
  Future<void> downloadMediaWithType(
      {String? specificUrl, MediaFileType? mediaFileType}) {
    return downloadMedia(
      specificUrl: specificUrl,
      fileType: mediaFileType?.extension,
    );
  }
}

/// Adapter for MediaPlayerController to implement DocumentDownloader
class MediaPlayerControllerAdapter implements DocumentDownloader {
  MediaPlayerControllerAdapter(this.controller);
  final MediaPlayerController controller;

  @override
  Future<void> downloadMedia({String? specificUrl, String? fileType}) {
    return controller.downloadMedia(
        specificUrl: specificUrl, fileType: fileType);
  }

  @override
  Future<void> downloadMediaWithType(
      {String? specificUrl, MediaFileType? mediaFileType}) {
    return downloadMedia(
      specificUrl: specificUrl,
      fileType: mediaFileType?.extension,
    );
  }
}

/// Adapter for PdfReaderNotifier to implement DocumentDownloader
class PdfReaderNotifierAdapter implements DocumentDownloader {
  PdfReaderNotifierAdapter(this.notifier);
  final PdfReaderNotifier notifier;

  @override
  Future<void> downloadMedia({String? specificUrl, String? fileType}) {
    return notifier.downloadMedia(specificUrl: specificUrl, fileType: fileType);
  }

  @override
  Future<void> downloadMediaWithType(
      {String? specificUrl, MediaFileType? mediaFileType}) {
    return downloadMedia(
      specificUrl: specificUrl,
      fileType: mediaFileType?.extension,
    );
  }
}

/// A beautiful dialog that displays download options for different document formats
class DocumentDownloadDialog extends ConsumerWidget {
  const DocumentDownloadDialog({
    super.key,
    required this.downloader,
    required this.mediaOptions,
  });
  final DocumentDownloader downloader;
  final List<MediaOption> mediaOptions;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ThemeData theme = Theme.of(context);
    final ColorScheme colorScheme = theme.colorScheme;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: const <BoxShadow>[
            BoxShadow(
              color: Colors.black26,
              blurRadius: 10.0,
              offset: Offset(0.0, 10.0),
            ),
          ],
        ),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              // Header
              Row(
                children: <Widget>[
                  Icon(
                    Icons.download_rounded,
                    color: colorScheme.primary,
                    size: 25,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Download Document',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                  // Cancel button
                  InkWell(
                    onTap: () => Navigator.of(context).pop(),
                    child: Icon(
                      Icons.close_rounded,
                      color: colorScheme.primary,
                      size: 25,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Divider(color: colorScheme.outlineVariant),
              const SizedBox(height: 8),
              Column(
                children: <Widget>[
                  // Description text
                  Text(
                    'Choose a format to download:',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Document format options
                  _buildDocumentOptions(context),

                  const SizedBox(height: 20),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDocumentOptions(BuildContext context) {
    final List<Widget> options = <Widget>[];
    final ThemeData theme = Theme.of(context);
    final ColorScheme colorScheme = theme.colorScheme;

    // Create a map to group options by file type
    final Map<MediaFileType, List<MediaOption>> groupedOptions =
        <MediaFileType, List<MediaOption>>{};

    // Process media options and group them by file type
    for (final MediaOption option in mediaOptions) {
      if (option.url != null && option.url!.isNotEmpty) {
        // Determine file type from format
        final MediaFileType? fileType = MediaFileType.fromFormat(option.format);

        // Skip unknown formats
        if (fileType == null) continue;

        // Add to the appropriate group
        if (!groupedOptions.containsKey(fileType)) {
          groupedOptions[fileType] = <MediaOption>[];
        }
        groupedOptions[fileType]!.add(option);
      }
    }

    // Add options for each file type
    for (final MediaFileType fileType in groupedOptions.keys) {
      _addOptionsForFileType(
        context: context,
        options: options,
        mediaOptions: groupedOptions[fileType]!,
        fileType: fileType,
      );
    }

    // If no options are available, show a message
    if (options.isEmpty) {
      return const Center(
        child: Text('No downloadable files available'),
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: options,
    );
  }

  /// Helper method to add options for a specific file type
  void _addOptionsForFileType({
    required BuildContext context,
    required List<Widget> options,
    required List<MediaOption> mediaOptions,
    required MediaFileType fileType,
  }) {
    final String title = fileType.displayTitle;
    final String defaultSubtitle = fileType.defaultSubtitle;
    final IconData icon = fileType.icon;
    final Color iconColor = fileType.iconColor;

    // If there's only one option, add it directly
    if (mediaOptions.length == 1) {
      options.add(
        _buildDownloadOption(
          context: context,
          icon: icon,
          iconColor: iconColor,
          title: title,
          subtitle: mediaOptions.first.description ?? defaultSubtitle,
          fileType: fileType.extension,
          fileUrl: mediaOptions.first.url!,
        ),
      );
      options.add(const SizedBox(height: 12));
      return;
    }

    // If there are multiple options, add a header
    options.add(
      Padding(
        padding: const EdgeInsets.only(left: 8.0, bottom: 4.0),
        child: Row(
          children: <Widget>[
            Icon(icon, color: iconColor, size: 18),
            const SizedBox(width: 8),
            Text(
              '$title Options',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ],
        ),
      ),
    );

    // Add each option with a unique identifier
    int index = 1;
    for (final MediaOption option in mediaOptions) {
      String subtitle = option.description ?? defaultSubtitle;

      // If no description is provided, add a number to differentiate
      if (option.description == null) {
        subtitle = '$defaultSubtitle (Option $index)';
      }

      options.add(
        _buildDownloadOption(
          context: context,
          icon: icon,
          iconColor: iconColor,
          title: '$title ${index > 1 ? index : ''}',
          subtitle: subtitle,
          fileType: fileType.extension,
          fileUrl: option.url!,
        ),
      );
      options.add(const SizedBox(
          height: 8)); // Smaller spacing between same type options
      index++;
    }

    options
        .add(const SizedBox(height: 4)); // Add some extra space after the group
  }

  Widget _buildDownloadOption({
    required BuildContext context,
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    required String fileType,
    required String fileUrl,
  }) {
    final ThemeData theme = Theme.of(context);
    final ColorScheme colorScheme = theme.colorScheme;

    // Get the MediaFileType from the fileType string
    final MediaFileType mediaFileType = MediaFileType.values.firstWhere(
      (MediaFileType type) => type.extension == fileType,
      orElse: () => MediaFileType.pdf, // Default to PDF if not found
    );

    // Extract a filename from the URL if possible
    String displayName = title;
    if (fileUrl.isNotEmpty) {
      try {
        final Uri uri = Uri.parse(fileUrl);
        final String path = uri.path;
        final String urlFileName = path.split('/').last;

        if (urlFileName.isNotEmpty && urlFileName.contains('.')) {
          displayName = urlFileName;
        }
      } catch (e) {
        // If URL parsing fails, just use the title
      }
    }

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          // Download the file using the enum
          downloader.downloadMediaWithType(
            specificUrl: fileUrl,
            mediaFileType: mediaFileType,
          );

          // Close the dialog
          Navigator.of(context).pop();

          // Show a snackbar with the actual filename if available
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Downloading $displayName...'),
              duration: const Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border:
                Border.all(color: colorScheme.outlineVariant.withOpacity(0.5)),
          ),
          child: Row(
            children: <Widget>[
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: iconColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: iconColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      displayName != title ? displayName : subtitle,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.download_rounded,
                color: colorScheme.primary,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Shows the document download dialog
void showDocumentDownloadDialog(
  BuildContext context,
  dynamic controller, {
  required List<MediaOption> mediaOptions,
}) {
  DocumentDownloader downloader;

  if (controller is MediaPlayerController) {
    downloader = MediaPlayerControllerAdapter(controller);
  } else if (controller is PdfReaderNotifier) {
    downloader = PdfReaderNotifierAdapter(controller);
  } else {
    throw ArgumentError(
        'Controller must be either MediaPlayerController or PdfReaderNotifier');
  }

  showDialog(
    context: context,
    builder: (BuildContext context) => DocumentDownloadDialog(
      downloader: downloader,
      mediaOptions: mediaOptions,
    ),
  );
}
