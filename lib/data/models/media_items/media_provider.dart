import 'package:flutter/widgets.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../repository/media_repository.dart';
import '../media_item.dart';
import '../media_ui_model.dart';

part 'media_provider.g.dart';

/// 1) AsyncNotifier for loading raw domain items
@Riverpod(keepAlive: true) // Keep alive to persist data across navigation
class MediaList extends _$MediaList {
  @override
  Future<List<MediaItem>> build() async {
    final MediaRepository repo = ref.read(mediaRepositoryProvider);
    try {
      final List<MediaItem> items = await repo.getAllMediaItems();
      // debugPrint(
      //     'mediaListProvider: Loaded ${items.length} items from repository');
      return items;
    } catch (e) {
      debugPrint('mediaListProvider: Error loading items: $e');
      // allow UI to show error
      throw Exception('Failed to load media items: $e');
    }
  }
}

/// 2) AsyncNotifier for converting domain → UI models with persistence
@Riverpod(keepAlive: true) // Keep the provider alive to persist UI models
class MediaUiList extends _$MediaUiList {
  @override
  Future<List<MediaUiModel>> build() async {
    final List<MediaItem> items = await ref.watch(mediaListProvider.future);

    final List<MediaUiModel> uiModels =
        items.map(MediaUiModel.fromDomain).toList();
    return uiModels;
  }
}

/// 3) Simple String state for search query with persistence
@Riverpod(keepAlive: true) // Keep the provider alive to persist search query
class MediaSearch extends _$MediaSearch {
  @override
  String build() => '';

  set value(String query) {
    // debugPrint('MediaSearch: Setting search query to "$query"');
    state = query;

    // If the query is cleared, invalidate the filtered media provider
    if (query.isEmpty) {
      // debugPrint('MediaSearch: Query cleared, invalidating providers');
      ref.invalidate(filteredMediaProvider);
    }
  }

  // Method to reset the search query
  void reset() {
    // debugPrint('MediaSearch: Resetting search query');
    state = '';
    // Don't invalidate filteredMediaProvider here to avoid circular dependency
    // The state change will automatically trigger filteredMediaProvider to rebuild
  }
}

/// 4) Computed provider that filters the UI list by the search query with persistence
@Riverpod(
    keepAlive: true) // Keep the provider alive to persist filtered results
List<MediaUiModel> filteredMedia(Ref ref) {
  final String query = ref.watch(mediaSearchProvider).toLowerCase();

  // Listen to changes in the search query
  ref.listen(mediaSearchProvider, (String? previous, String current) {
    // If the query was cleared, we might want to invalidate this provider
    if (previous != null && previous.isNotEmpty && current.isEmpty) {
      debugPrint('filteredMedia: Query cleared, will return all items');
    }
  });

  return ref.watch(mediaUiListProvider).when(
    data: (List<MediaUiModel> data) {
      // If query is empty, return all data without filtering
      if (query.isEmpty) {
        // debugPrint(
        //     'filteredMedia: Empty query, returning all ${data.length} items');
        return data;
      }

      final List<MediaUiModel> filtered = data.where((MediaUiModel item) {
        // Search in title
        if (item.title.toLowerCase().contains(query)) {
          return true;
        }

        // Search in type and category
        if (item.type.toLowerCase().contains(query) ||
            item.category.toLowerCase().contains(query)) {
          return true;
        }

        // Search in metadata description if available
        if (item.metadata != null) {
          // Check for description in metadata
          if (item.metadata!.description != null &&
              item.metadata!.description!.toLowerCase().contains(query)) {
            return true;
          }

          // Check for HTML content in metadata
          if (item.metadata!.html != null &&
              item.metadata!.html!.toLowerCase().contains(query)) {
            return true;
          }
        }

        // Search in item description if available
        if (item.description != null &&
            item.description!.toLowerCase().contains(query)) {
          return true;
        }

        // Search in article text if available
        if (item.articleText != null &&
            item.articleText!.toLowerCase().contains(query)) {
          return true;
        }

        // Search in tweet content if available
        if (item.tweetContent != null &&
            item.tweetContent!.toLowerCase().contains(query)) {
          return true;
        }

        return false;
      }).toList();

      return filtered;
    },
    loading: () {
      return <MediaUiModel>[];
    },
    error: (Object error, StackTrace stackTrace) {
      debugPrint('filteredMedia: Error state - $error');
      return <MediaUiModel>[];
    },
  );
}
