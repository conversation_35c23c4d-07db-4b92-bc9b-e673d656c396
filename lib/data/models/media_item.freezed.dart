// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'media_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MediaItem {
  String get id;
  String get slug;
  String get type;
  String get category;
  String get title;
  String? get description;
  String get language;
  String get searchableText;
  DateTime get createdAt;
  DateTime get updatedAt;
  int? get durationSeconds;
  String? get thumbnailUrl;
  String? get license;
  List<MediaOption> get options;
  @MediaMetadataConverter()
  MediaMetadata? get metadata;

  /// Create a copy of MediaItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MediaItemCopyWith<MediaItem> get copyWith =>
      _$MediaItemCopyWithImpl<MediaItem>(this as MediaItem, _$identity);

  /// Serializes this MediaItem to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MediaItem &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.slug, slug) || other.slug == slug) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.language, language) ||
                other.language == language) &&
            (identical(other.searchableText, searchableText) ||
                other.searchableText == searchableText) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.durationSeconds, durationSeconds) ||
                other.durationSeconds == durationSeconds) &&
            (identical(other.thumbnailUrl, thumbnailUrl) ||
                other.thumbnailUrl == thumbnailUrl) &&
            (identical(other.license, license) || other.license == license) &&
            const DeepCollectionEquality().equals(other.options, options) &&
            (identical(other.metadata, metadata) ||
                other.metadata == metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      slug,
      type,
      category,
      title,
      description,
      language,
      searchableText,
      createdAt,
      updatedAt,
      durationSeconds,
      thumbnailUrl,
      license,
      const DeepCollectionEquality().hash(options),
      metadata);

  @override
  String toString() {
    return 'MediaItem(id: $id, slug: $slug, type: $type, category: $category, title: $title, description: $description, language: $language, searchableText: $searchableText, createdAt: $createdAt, updatedAt: $updatedAt, durationSeconds: $durationSeconds, thumbnailUrl: $thumbnailUrl, license: $license, options: $options, metadata: $metadata)';
  }
}

/// @nodoc
abstract mixin class $MediaItemCopyWith<$Res> {
  factory $MediaItemCopyWith(MediaItem value, $Res Function(MediaItem) _then) =
      _$MediaItemCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String slug,
      String type,
      String category,
      String title,
      String? description,
      String language,
      String searchableText,
      DateTime createdAt,
      DateTime updatedAt,
      int? durationSeconds,
      String? thumbnailUrl,
      String? license,
      List<MediaOption> options,
      @MediaMetadataConverter() MediaMetadata? metadata});

  $MediaMetadataCopyWith<$Res>? get metadata;
}

/// @nodoc
class _$MediaItemCopyWithImpl<$Res> implements $MediaItemCopyWith<$Res> {
  _$MediaItemCopyWithImpl(this._self, this._then);

  final MediaItem _self;
  final $Res Function(MediaItem) _then;

  /// Create a copy of MediaItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? slug = null,
    Object? type = null,
    Object? category = null,
    Object? title = null,
    Object? description = freezed,
    Object? language = null,
    Object? searchableText = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? durationSeconds = freezed,
    Object? thumbnailUrl = freezed,
    Object? license = freezed,
    Object? options = null,
    Object? metadata = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      slug: null == slug
          ? _self.slug
          : slug // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _self.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      language: null == language
          ? _self.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
      searchableText: null == searchableText
          ? _self.searchableText
          : searchableText // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      durationSeconds: freezed == durationSeconds
          ? _self.durationSeconds
          : durationSeconds // ignore: cast_nullable_to_non_nullable
              as int?,
      thumbnailUrl: freezed == thumbnailUrl
          ? _self.thumbnailUrl
          : thumbnailUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      license: freezed == license
          ? _self.license
          : license // ignore: cast_nullable_to_non_nullable
              as String?,
      options: null == options
          ? _self.options
          : options // ignore: cast_nullable_to_non_nullable
              as List<MediaOption>,
      metadata: freezed == metadata
          ? _self.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as MediaMetadata?,
    ));
  }

  /// Create a copy of MediaItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MediaMetadataCopyWith<$Res>? get metadata {
    if (_self.metadata == null) {
      return null;
    }

    return $MediaMetadataCopyWith<$Res>(_self.metadata!, (value) {
      return _then(_self.copyWith(metadata: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _MediaItem implements MediaItem {
  const _MediaItem(
      {required this.id,
      required this.slug,
      required this.type,
      required this.category,
      required this.title,
      this.description,
      required this.language,
      required this.searchableText,
      required this.createdAt,
      required this.updatedAt,
      this.durationSeconds,
      this.thumbnailUrl,
      this.license,
      required final List<MediaOption> options,
      @MediaMetadataConverter() this.metadata})
      : _options = options;
  factory _MediaItem.fromJson(Map<String, dynamic> json) =>
      _$MediaItemFromJson(json);

  @override
  final String id;
  @override
  final String slug;
  @override
  final String type;
  @override
  final String category;
  @override
  final String title;
  @override
  final String? description;
  @override
  final String language;
  @override
  final String searchableText;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  final int? durationSeconds;
  @override
  final String? thumbnailUrl;
  @override
  final String? license;
  final List<MediaOption> _options;
  @override
  List<MediaOption> get options {
    if (_options is EqualUnmodifiableListView) return _options;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_options);
  }

  @override
  @MediaMetadataConverter()
  final MediaMetadata? metadata;

  /// Create a copy of MediaItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MediaItemCopyWith<_MediaItem> get copyWith =>
      __$MediaItemCopyWithImpl<_MediaItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MediaItemToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MediaItem &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.slug, slug) || other.slug == slug) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.language, language) ||
                other.language == language) &&
            (identical(other.searchableText, searchableText) ||
                other.searchableText == searchableText) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.durationSeconds, durationSeconds) ||
                other.durationSeconds == durationSeconds) &&
            (identical(other.thumbnailUrl, thumbnailUrl) ||
                other.thumbnailUrl == thumbnailUrl) &&
            (identical(other.license, license) || other.license == license) &&
            const DeepCollectionEquality().equals(other._options, _options) &&
            (identical(other.metadata, metadata) ||
                other.metadata == metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      slug,
      type,
      category,
      title,
      description,
      language,
      searchableText,
      createdAt,
      updatedAt,
      durationSeconds,
      thumbnailUrl,
      license,
      const DeepCollectionEquality().hash(_options),
      metadata);

  @override
  String toString() {
    return 'MediaItem(id: $id, slug: $slug, type: $type, category: $category, title: $title, description: $description, language: $language, searchableText: $searchableText, createdAt: $createdAt, updatedAt: $updatedAt, durationSeconds: $durationSeconds, thumbnailUrl: $thumbnailUrl, license: $license, options: $options, metadata: $metadata)';
  }
}

/// @nodoc
abstract mixin class _$MediaItemCopyWith<$Res>
    implements $MediaItemCopyWith<$Res> {
  factory _$MediaItemCopyWith(
          _MediaItem value, $Res Function(_MediaItem) _then) =
      __$MediaItemCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String slug,
      String type,
      String category,
      String title,
      String? description,
      String language,
      String searchableText,
      DateTime createdAt,
      DateTime updatedAt,
      int? durationSeconds,
      String? thumbnailUrl,
      String? license,
      List<MediaOption> options,
      @MediaMetadataConverter() MediaMetadata? metadata});

  @override
  $MediaMetadataCopyWith<$Res>? get metadata;
}

/// @nodoc
class __$MediaItemCopyWithImpl<$Res> implements _$MediaItemCopyWith<$Res> {
  __$MediaItemCopyWithImpl(this._self, this._then);

  final _MediaItem _self;
  final $Res Function(_MediaItem) _then;

  /// Create a copy of MediaItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? slug = null,
    Object? type = null,
    Object? category = null,
    Object? title = null,
    Object? description = freezed,
    Object? language = null,
    Object? searchableText = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? durationSeconds = freezed,
    Object? thumbnailUrl = freezed,
    Object? license = freezed,
    Object? options = null,
    Object? metadata = freezed,
  }) {
    return _then(_MediaItem(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      slug: null == slug
          ? _self.slug
          : slug // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _self.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      language: null == language
          ? _self.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
      searchableText: null == searchableText
          ? _self.searchableText
          : searchableText // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      durationSeconds: freezed == durationSeconds
          ? _self.durationSeconds
          : durationSeconds // ignore: cast_nullable_to_non_nullable
              as int?,
      thumbnailUrl: freezed == thumbnailUrl
          ? _self.thumbnailUrl
          : thumbnailUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      license: freezed == license
          ? _self.license
          : license // ignore: cast_nullable_to_non_nullable
              as String?,
      options: null == options
          ? _self._options
          : options // ignore: cast_nullable_to_non_nullable
              as List<MediaOption>,
      metadata: freezed == metadata
          ? _self.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as MediaMetadata?,
    ));
  }

  /// Create a copy of MediaItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MediaMetadataCopyWith<$Res>? get metadata {
    if (_self.metadata == null) {
      return null;
    }

    return $MediaMetadataCopyWith<$Res>(_self.metadata!, (value) {
      return _then(_self.copyWith(metadata: value));
    });
  }
}

/// @nodoc
mixin _$MediaOption {
  String get optionId;
  String get format;
  String? get url;
  String? get description;
  String? get role;
  int? get bitrate; // HTML content field
  String? get html; // New fields for tweets
  @JsonKey(name: 'post_link')
  String? get postLink;
  @JsonKey(name: 'post_content')
  String? get postContent;
  @JsonKey(name: 'user_info')
  Map<String, dynamic>? get userInfo;
  @JsonKey(name: 'post_image')
  String? get postImage;

  /// Create a copy of MediaOption
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MediaOptionCopyWith<MediaOption> get copyWith =>
      _$MediaOptionCopyWithImpl<MediaOption>(this as MediaOption, _$identity);

  /// Serializes this MediaOption to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MediaOption &&
            (identical(other.optionId, optionId) ||
                other.optionId == optionId) &&
            (identical(other.format, format) || other.format == format) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.bitrate, bitrate) || other.bitrate == bitrate) &&
            (identical(other.html, html) || other.html == html) &&
            (identical(other.postLink, postLink) ||
                other.postLink == postLink) &&
            (identical(other.postContent, postContent) ||
                other.postContent == postContent) &&
            const DeepCollectionEquality().equals(other.userInfo, userInfo) &&
            (identical(other.postImage, postImage) ||
                other.postImage == postImage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      optionId,
      format,
      url,
      description,
      role,
      bitrate,
      html,
      postLink,
      postContent,
      const DeepCollectionEquality().hash(userInfo),
      postImage);

  @override
  String toString() {
    return 'MediaOption(optionId: $optionId, format: $format, url: $url, description: $description, role: $role, bitrate: $bitrate, html: $html, postLink: $postLink, postContent: $postContent, userInfo: $userInfo, postImage: $postImage)';
  }
}

/// @nodoc
abstract mixin class $MediaOptionCopyWith<$Res> {
  factory $MediaOptionCopyWith(
          MediaOption value, $Res Function(MediaOption) _then) =
      _$MediaOptionCopyWithImpl;
  @useResult
  $Res call(
      {String optionId,
      String format,
      String? url,
      String? description,
      String? role,
      int? bitrate,
      String? html,
      @JsonKey(name: 'post_link') String? postLink,
      @JsonKey(name: 'post_content') String? postContent,
      @JsonKey(name: 'user_info') Map<String, dynamic>? userInfo,
      @JsonKey(name: 'post_image') String? postImage});
}

/// @nodoc
class _$MediaOptionCopyWithImpl<$Res> implements $MediaOptionCopyWith<$Res> {
  _$MediaOptionCopyWithImpl(this._self, this._then);

  final MediaOption _self;
  final $Res Function(MediaOption) _then;

  /// Create a copy of MediaOption
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? optionId = null,
    Object? format = null,
    Object? url = freezed,
    Object? description = freezed,
    Object? role = freezed,
    Object? bitrate = freezed,
    Object? html = freezed,
    Object? postLink = freezed,
    Object? postContent = freezed,
    Object? userInfo = freezed,
    Object? postImage = freezed,
  }) {
    return _then(_self.copyWith(
      optionId: null == optionId
          ? _self.optionId
          : optionId // ignore: cast_nullable_to_non_nullable
              as String,
      format: null == format
          ? _self.format
          : format // ignore: cast_nullable_to_non_nullable
              as String,
      url: freezed == url
          ? _self.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      role: freezed == role
          ? _self.role
          : role // ignore: cast_nullable_to_non_nullable
              as String?,
      bitrate: freezed == bitrate
          ? _self.bitrate
          : bitrate // ignore: cast_nullable_to_non_nullable
              as int?,
      html: freezed == html
          ? _self.html
          : html // ignore: cast_nullable_to_non_nullable
              as String?,
      postLink: freezed == postLink
          ? _self.postLink
          : postLink // ignore: cast_nullable_to_non_nullable
              as String?,
      postContent: freezed == postContent
          ? _self.postContent
          : postContent // ignore: cast_nullable_to_non_nullable
              as String?,
      userInfo: freezed == userInfo
          ? _self.userInfo
          : userInfo // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      postImage: freezed == postImage
          ? _self.postImage
          : postImage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _MediaOption implements MediaOption {
  const _MediaOption(
      {required this.optionId,
      required this.format,
      this.url,
      this.description,
      this.role,
      this.bitrate,
      this.html,
      @JsonKey(name: 'post_link') this.postLink,
      @JsonKey(name: 'post_content') this.postContent,
      @JsonKey(name: 'user_info') final Map<String, dynamic>? userInfo,
      @JsonKey(name: 'post_image') this.postImage})
      : _userInfo = userInfo;
  factory _MediaOption.fromJson(Map<String, dynamic> json) =>
      _$MediaOptionFromJson(json);

  @override
  final String optionId;
  @override
  final String format;
  @override
  final String? url;
  @override
  final String? description;
  @override
  final String? role;
  @override
  final int? bitrate;
// HTML content field
  @override
  final String? html;
// New fields for tweets
  @override
  @JsonKey(name: 'post_link')
  final String? postLink;
  @override
  @JsonKey(name: 'post_content')
  final String? postContent;
  final Map<String, dynamic>? _userInfo;
  @override
  @JsonKey(name: 'user_info')
  Map<String, dynamic>? get userInfo {
    final value = _userInfo;
    if (value == null) return null;
    if (_userInfo is EqualUnmodifiableMapView) return _userInfo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  @JsonKey(name: 'post_image')
  final String? postImage;

  /// Create a copy of MediaOption
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MediaOptionCopyWith<_MediaOption> get copyWith =>
      __$MediaOptionCopyWithImpl<_MediaOption>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MediaOptionToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MediaOption &&
            (identical(other.optionId, optionId) ||
                other.optionId == optionId) &&
            (identical(other.format, format) || other.format == format) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.bitrate, bitrate) || other.bitrate == bitrate) &&
            (identical(other.html, html) || other.html == html) &&
            (identical(other.postLink, postLink) ||
                other.postLink == postLink) &&
            (identical(other.postContent, postContent) ||
                other.postContent == postContent) &&
            const DeepCollectionEquality().equals(other._userInfo, _userInfo) &&
            (identical(other.postImage, postImage) ||
                other.postImage == postImage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      optionId,
      format,
      url,
      description,
      role,
      bitrate,
      html,
      postLink,
      postContent,
      const DeepCollectionEquality().hash(_userInfo),
      postImage);

  @override
  String toString() {
    return 'MediaOption(optionId: $optionId, format: $format, url: $url, description: $description, role: $role, bitrate: $bitrate, html: $html, postLink: $postLink, postContent: $postContent, userInfo: $userInfo, postImage: $postImage)';
  }
}

/// @nodoc
abstract mixin class _$MediaOptionCopyWith<$Res>
    implements $MediaOptionCopyWith<$Res> {
  factory _$MediaOptionCopyWith(
          _MediaOption value, $Res Function(_MediaOption) _then) =
      __$MediaOptionCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String optionId,
      String format,
      String? url,
      String? description,
      String? role,
      int? bitrate,
      String? html,
      @JsonKey(name: 'post_link') String? postLink,
      @JsonKey(name: 'post_content') String? postContent,
      @JsonKey(name: 'user_info') Map<String, dynamic>? userInfo,
      @JsonKey(name: 'post_image') String? postImage});
}

/// @nodoc
class __$MediaOptionCopyWithImpl<$Res> implements _$MediaOptionCopyWith<$Res> {
  __$MediaOptionCopyWithImpl(this._self, this._then);

  final _MediaOption _self;
  final $Res Function(_MediaOption) _then;

  /// Create a copy of MediaOption
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? optionId = null,
    Object? format = null,
    Object? url = freezed,
    Object? description = freezed,
    Object? role = freezed,
    Object? bitrate = freezed,
    Object? html = freezed,
    Object? postLink = freezed,
    Object? postContent = freezed,
    Object? userInfo = freezed,
    Object? postImage = freezed,
  }) {
    return _then(_MediaOption(
      optionId: null == optionId
          ? _self.optionId
          : optionId // ignore: cast_nullable_to_non_nullable
              as String,
      format: null == format
          ? _self.format
          : format // ignore: cast_nullable_to_non_nullable
              as String,
      url: freezed == url
          ? _self.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      role: freezed == role
          ? _self.role
          : role // ignore: cast_nullable_to_non_nullable
              as String?,
      bitrate: freezed == bitrate
          ? _self.bitrate
          : bitrate // ignore: cast_nullable_to_non_nullable
              as int?,
      html: freezed == html
          ? _self.html
          : html // ignore: cast_nullable_to_non_nullable
              as String?,
      postLink: freezed == postLink
          ? _self.postLink
          : postLink // ignore: cast_nullable_to_non_nullable
              as String?,
      postContent: freezed == postContent
          ? _self.postContent
          : postContent // ignore: cast_nullable_to_non_nullable
              as String?,
      userInfo: freezed == userInfo
          ? _self._userInfo
          : userInfo // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      postImage: freezed == postImage
          ? _self.postImage
          : postImage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
