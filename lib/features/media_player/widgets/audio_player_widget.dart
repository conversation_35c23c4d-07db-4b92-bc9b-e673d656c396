import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';

class AudioPlayerWidget extends StatelessWidget {
  const AudioPlayerWidget({
    required this.thumbnailUrl,
    required this.isPlaying,
    this.audioPlayer,
    this.isBuffering = false,
    super.key,
  });

  final String? thumbnailUrl;
  final bool isPlaying;
  final bool isBuffering;
  final AudioPlayer? audioPlayer;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          if (thumbnailUrl != null)
            Container(
              width: 250,
              height: 250,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: <BoxShadow>[
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    spreadRadius: 2,
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.network(
                  thumbnailUrl!,
                  fit: BoxFit.cover,
                  errorBuilder: (BuildContext context, Object error,
                      StackTrace? stackTrace) {
                    return Container(
                      color:
                          Theme.of(context).colorScheme.surfaceContainerHighest,
                      child: const Icon(
                        Icons.audiotrack,
                        size: 80,
                        color: Colors.white54,
                      ),
                    );
                  },
                ),
              ),
            )
          else
            Container(
              width: 250,
              height: 250,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.audiotrack,
                size: 80,
                color: Colors.white54,
              ),
            ),
          const SizedBox(height: 20),
          if (isBuffering)
            // Show loading spinner when buffering
            const SizedBox(
              width: 50,
              height: 50,
              child: CircularProgressIndicator(),
            )
          else if (isPlaying)
            // Show audio wave animation when playing
            const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                _AudioWaveBar(height: 30),
                SizedBox(width: 5),
                _AudioWaveBar(height: 45),
                SizedBox(width: 5),
                _AudioWaveBar(height: 20),
                SizedBox(width: 5),
                _AudioWaveBar(height: 40),
                SizedBox(width: 5),
                _AudioWaveBar(height: 25),
              ],
            ),
        ],
      ),
    );
  }
}

class _AudioWaveBar extends StatelessWidget {
  const _AudioWaveBar({
    required this.height,
  });

  final double height;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 4,
      height: height,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        borderRadius: BorderRadius.circular(5),
      ),
    );
  }
}
