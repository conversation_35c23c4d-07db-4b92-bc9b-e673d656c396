import 'package:freezed_annotation/freezed_annotation.dart';

import '../enums/media_type_enum.dart';
import 'media_item_converter.dart';
import 'media_metadata.dart';

part 'media_item.freezed.dart';
part 'media_item.g.dart';

@freezed
abstract class MediaItem with _$MediaItem {
  const factory MediaItem({
    required String id,
    required String slug,
    required String type,
    required String category,
    required String title,
    String? description,
    required String language,
    required String searchableText,
    required DateTime createdAt,
    required DateTime updatedAt,
    int? durationSeconds,
    String? thumbnailUrl,
    String? license,
    required List<MediaOption> options,
    @MediaMetadataConverter() MediaMetadata? metadata,
  }) = _MediaItem;

  factory MediaItem.fromJson(Map<String, dynamic> json) =>
      _$MediaItemFromJson(json);
}

@freezed
abstract class MediaOption with _$MediaOption {
  const factory MediaOption({
    required String optionId,
    required String format,
    String? url,
    String? description,
    String? role,
    int? bitrate,
    // HTML content field
    String? html,
    // New fields for tweets
    @<PERSON><PERSON><PERSON><PERSON>(name: 'post_link') String? postLink,
    @JsonKey(name: 'post_content') String? postContent,
    @JsonKey(name: 'user_info') Map<String, dynamic>? userInfo,
    @JsonKey(name: 'post_image') String? postImage,
  }) = _MediaOption;

  factory MediaOption.fromJson(Map<String, dynamic> json) =>
      _$MediaOptionFromJson(json);
}

/// Extension to add helper methods to MediaOption
extension MediaOptionExtensions on MediaOption {
  // Check format types
  bool get isAudio => format.startsWith('audio');
  bool get isVideo => format.startsWith('video');
  bool get isPdf => format.contains('pdf');
  bool get isDocument => format.contains('doc') || isPdf;
  bool get isHtml => format == 'html';
  bool get isTweet => format == 'tweet';
  bool get isImage => format.startsWith('image');

  // Get quality indicator (higher is better)
  int get qualityScore {
    if (bitrate != null) {
      return bitrate!;
    }
    if (format.contains('hd') || format.contains('1080')) {
      return 1000;
    }
    if (format.contains('720')) {
      return 720;
    }
    return 0;
  }

  // Check if this option has valid content
  bool get hasValidContent {
    return (url != null && url!.isNotEmpty) ||
        (html != null && html!.isNotEmpty) ||
        (postContent != null && postContent!.isNotEmpty);
  }

  // Get tweet author name safely
  String? get tweetAuthorName {
    if (userInfo == null) {
      return null;
    }
    return userInfo!['name'] as String?;
  }

  // Get tweet author ID safely
  String? get tweetAuthorId {
    if (userInfo == null) {
      return null;
    }
    return userInfo!['id'] as String?;
  }

  // Get tweet author page link safely
  String? get tweetAuthorPageLink {
    if (userInfo == null) {
      return null;
    }
    return userInfo!['page_link'] as String?;
  }
}

/// Extension to add helper methods to MediaItem
extension MediaItemExtensions on MediaItem {
  // Computed properties for better type safety
  MediaType get mediaType => MediaType.fromString(type);

  // Helper methods for common operations
  bool get hasAudio =>
      options.any((MediaOption opt) => opt.format.startsWith('audio'));
  bool get hasVideo =>
      options.any((MediaOption opt) => opt.format.startsWith('video'));
  bool get hasPdf =>
      options.any((MediaOption opt) => opt.format.contains('pdf'));
  bool get hasHtml => options.any((MediaOption opt) => opt.format == 'html');
  bool get isTweet => mediaType == MediaType.tweet;

  // Get specific option types
  MediaOption? get primaryAudioOption => options
      .where((MediaOption opt) => opt.format.startsWith('audio'))
      .firstOrNull;
  MediaOption? get primaryVideoOption => options
      .where((MediaOption opt) => opt.format.startsWith('video'))
      .firstOrNull;
  MediaOption? get primaryDocumentOption => options
      .where((MediaOption opt) =>
          opt.format.contains('pdf') || opt.format.contains('doc'))
      .firstOrNull;
  MediaOption? get tweetOption =>
      options.where((MediaOption opt) => opt.format == 'tweet').firstOrNull;
  MediaOption? get htmlOption =>
      options.where((MediaOption opt) => opt.format == 'html').firstOrNull;

  // Validation methods
  bool get isValid => id.isNotEmpty && title.isNotEmpty && options.isNotEmpty;

  // Search helper
  bool matchesQuery(String query) {
    final String lowerQuery = query.toLowerCase();
    return title.toLowerCase().contains(lowerQuery) ||
        (description?.toLowerCase().contains(lowerQuery) ?? false) ||
        searchableText.toLowerCase().contains(lowerQuery);
  }

  // Get all URLs for a specific format type
  List<String> getUrlsForFormat(String formatPrefix) {
    return options
        .where((MediaOption opt) => opt.format.startsWith(formatPrefix))
        .map((MediaOption opt) => opt.url)
        .where((String? url) => url != null && url.isNotEmpty)
        .cast<String>()
        .toList();
  }

  // Get the best quality option for a format
  MediaOption? getBestQualityOption(String formatPrefix) {
    final List<MediaOption> matchingOptions = options
        .where((MediaOption opt) => opt.format.startsWith(formatPrefix))
        .toList();

    if (matchingOptions.isEmpty) {
      return null;
    }

    // Sort by bitrate (higher is better) or return first if no bitrate info
    matchingOptions.sort((MediaOption a, MediaOption b) {
      if (a.bitrate == null && b.bitrate == null) {
        return 0;
      }
      if (a.bitrate == null) {
        return 1;
      }
      if (b.bitrate == null) {
        return -1;
      }
      return b.bitrate!.compareTo(a.bitrate!);
    });

    return matchingOptions.first;
  }
}
