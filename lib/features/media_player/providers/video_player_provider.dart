import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

import '../../../data/enums/video_url_types_enum.dart';

part 'video_player_provider.g.dart';

/// State class for the video player
class VideoPlayerState {
  const VideoPlayerState({
    this.videoType = VideoPlayerType.unknown,
    this.videoUrl,
    this.youtubeId,
    this.isFullScreen = false,
    this.isLoading = true,
    this.errorMessage,
    this.youtubeController,
    this.webViewController,
  });
  final VideoPlayerType videoType;
  final String? videoUrl;
  final String? youtubeId;
  final bool isFullScreen;
  final bool isLoading;
  final String? errorMessage;
  final YoutubePlayerController? youtubeController;
  final WebViewController? webViewController;

  /// Creates a copy of this state with the given fields replaced
  VideoPlayerState copyWith({
    VideoPlayerType? videoType,
    String? videoUrl,
    String? youtubeId,
    bool? isFullScreen,
    bool? isLoading,
    String? errorMessage,
    YoutubePlayerController? youtubeController,
    WebViewController? webViewController,
  }) {
    return VideoPlayerState(
      videoType: videoType ?? this.videoType,
      videoUrl: videoUrl ?? this.videoUrl,
      youtubeId: youtubeId ?? this.youtubeId,
      isFullScreen: isFullScreen ?? this.isFullScreen,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      youtubeController: youtubeController ?? this.youtubeController,
      webViewController: webViewController ?? this.webViewController,
    );
  }
}

/// Provider for the video player state
@riverpod
class VideoPlayerNotifier extends _$VideoPlayerNotifier {
  @override
  VideoPlayerState build() {
    return const VideoPlayerState();
  }

  /// Sets the video URL and determines the video type
  void setVideoUrl(String? url) {
    if (url == null || url.isEmpty) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'No video URL available',
      );
      return;
    }

    state = state.copyWith(videoUrl: url);
    _determineVideoType(url);
  }

  /// Determines the type of video based on the URL
  void _determineVideoType(String url) {
    // Check for direct video files
    if (url.toLowerCase().endsWith('.mp4') ||
        url.toLowerCase().endsWith('.webm') ||
        url.toLowerCase().endsWith('.mov') ||
        url.toLowerCase().endsWith('.avi')) {
      state = state.copyWith(
        videoType: VideoPlayerType.direct,
        isLoading: false,
      );
      return;
    }

    // Check for YouTube
    if (url.contains('youtube.com') || url.contains('youtu.be')) {
      try {
        final String? youtubeId = YoutubePlayer.convertUrlToId(url);
        if (youtubeId != null) {
          _initializeYoutubePlayer(youtubeId);
          state = state.copyWith(
            videoType: VideoPlayerType.youtube,
            youtubeId: youtubeId,
          );
          return;
        }
      } catch (e) {
        debugPrint('Error extracting YouTube ID: $e');
      }
    }

    // Check for Facebook
    if (url.contains('facebook.com') || url.contains('fb.watch')) {
      state = state.copyWith(
        videoType: VideoPlayerType.facebook,
        isLoading: false,
      );
      return;
    }

    // Check for TikTok
    if (url.contains('tiktok.com')) {
      state = state.copyWith(
        videoType: VideoPlayerType.tiktok,
        isLoading: false,
      );
      return;
    }

    // For other URLs, try to play directly
    state = state.copyWith(
      videoType: VideoPlayerType.direct,
      isLoading: false,
    );
  }

  /// Initializes the YouTube player
  void _initializeYoutubePlayer(String youtubeId) {
    final YoutubePlayerController controller = YoutubePlayerController(
      initialVideoId: youtubeId,
      flags: const YoutubePlayerFlags(
        autoPlay: false,
      ),
    );

    state = state.copyWith(
      youtubeController: controller,
      isLoading: false,
    );
  }

  /// Initializes the WebView controller for web-based videos
  void initializeWebViewController() {
    if (state.videoUrl == null) {
      return;
    }

    // Create the controller first
    final WebViewController controller = WebViewController();

    // Configure the controller
    controller
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.black)
      ..loadRequest(Uri.parse(state.videoUrl!))
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            debugPrint('Page started loading: $url');
            setLoading(true);
          },
          onPageFinished: (String url) {
            debugPrint('Page finished loading: $url');
            setLoading(false);

            // Inject custom CSS for better video display
            if (state.videoType == VideoPlayerType.facebook) {
              _injectFacebookVideoCSS(controller);
            } else if (state.videoType == VideoPlayerType.tiktok) {
              _injectTikTokVideoCSS(controller);
            }
          },
          onWebResourceError: (WebResourceError error) {
            debugPrint('WebView error: ${error.description}');
          },
        ),
      );

    state = state.copyWith(webViewController: controller);
  }

  /// Injects CSS for Facebook videos
  void _injectFacebookVideoCSS(WebViewController webViewController) {
    const String css = '''
      .video-player { width: 100% !important; height: auto !important; }
      .video-container { width: 100% !important; }
    ''';
    webViewController
        .runJavaScript('var style = document.createElement("style"); '
            'style.textContent = `$css`; '
            'document.head.appendChild(style);');
  }

  /// Injects CSS for TikTok videos
  void _injectTikTokVideoCSS(WebViewController webViewController) {
    const String css = '''
      .video-feed { width: 100% !important; height: auto !important; }
      .video-player { width: 100% !important; }
    ''';
    webViewController
        .runJavaScript('var style = document.createElement("style"); '
            'style.textContent = `$css`; '
            'document.head.appendChild(style);');
  }

  /// Sets the loading state
  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  /// Sets an error message
  void setError(String message) {
    state = state.copyWith(
      errorMessage: message,
      isLoading: false,
    );
  }

  /// Cleans up resources when the provider is no longer needed
  void cleanUp() {
    if (state.youtubeController != null) {
      state.youtubeController!.dispose();
    }
  }
}
