import 'dart:math';

import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'media_item.dart';
import 'media_item_converter.dart';
import 'media_items/media_type_enum.dart';
import 'media_metadata.dart';

part 'media_ui_model.freezed.dart';
part 'media_ui_model.g.dart';

@freezed
abstract class MediaUiModel with _$MediaUiModel {
  const factory MediaUiModel({
    required String id,
    required String title,
    required String type,
    required String category,
    String? thumbnailUrl,
    String? audioUrl,
    String? videoUrl,
    String? documentUrl, // For PDF, DOC, etc.
    String? articleText, // For article content
    String? tweetContent, // For tweet content
    String? tweetAuthor, // For tweet author
    DateTime? tweetDate, // For tweet date
    String? tweetPostLink, // Link to the original tweet
    String? tweetImageUrl, // Image in the tweet
    String? tweetUserId, // User ID of the tweet author
    String? tweetUserPageLink, // Link to the author's profile
    @MediaMetadataConverter()
    MediaMetadata?
        metadata, // Using the new typed MediaMetadata class with converter
    String? description,
    List<MediaOption>? options, // Options for different formats
  }) = _MediaUiModel;

  factory MediaUiModel.fromJson(Map<String, dynamic> json) =>
      _$MediaUiModelFromJson(json);

  factory MediaUiModel.fromDomain(MediaItem item) {
    // Find audio option
    final MediaOption audioOption = item.options.firstWhere(
      (MediaOption opt) => opt.format.startsWith('audio'),
      orElse: () =>
          const MediaOption(optionId: 'default', format: 'audio/mpeg'),
    );

    // Find video option
    final MediaOption videoOption = item.options.firstWhere(
      (MediaOption opt) => opt.format.startsWith('video'),
      orElse: () => const MediaOption(optionId: 'default', format: 'video/mp4'),
    );

    // First, try to find a PDF document option
    final MediaOption pdfOption = item.options.firstWhere(
      (MediaOption opt) =>
          opt.format.startsWith('application/pdf') ||
          opt.format.contains('pdf'),
      orElse: () =>
          const MediaOption(optionId: 'default', format: 'application/pdf'),
    );

    if (pdfOption.optionId.isNotEmpty) {
      // debugPrint('PDF option found: $pdfOption');
    } else {
      // debugPrint('No PDF option found');
    }

    // Then, find any document option (PDF, DOC, etc.)
    final MediaOption documentOption = item.options.firstWhere(
      (MediaOption opt) =>
          opt.format.startsWith('application/pdf') ||
          opt.format.contains('document') ||
          opt.format.contains('pdf') ||
          opt.format.contains('text/plain') ||
          opt.format.contains('application/msword') ||
          opt.format.contains(
              'application/vnd.openxmlformats-officedocument.wordprocessingml.document'),
      orElse: () =>
          const MediaOption(optionId: 'default', format: 'application/pdf'),
    );

    // Debug log document option
    // debugPrint('Document option found: $documentOption');
    // debugPrint('Document URL: ${documentOption.url}');

    // Store all document options for later use
    final List<MediaOption> allDocumentOptions = item.options
        .where(
          (MediaOption opt) =>
              opt.format.startsWith('application/pdf') ||
              opt.format.contains('document') ||
              opt.format.contains('pdf') ||
              opt.format.contains('text/plain') ||
              opt.format.contains('application/msword') ||
              opt.format.contains(
                  'application/vnd.openxmlformats-officedocument.wordprocessingml.document'),
        )
        .toList();

    // debugPrint('All document options: $allDocumentOptions');

    // Check for document options in the format field
    for (final MediaOption opt in item.options) {
      // debugPrint('Checking option: ${opt.optionId}, format: ${opt.format}');
      if (opt.format.contains(
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document')) {
        // debugPrint('Found DOCX option: ${opt.url}');
        if (!allDocumentOptions.contains(opt)) {
          allDocumentOptions.add(opt);
        }
      } else if (opt.format.contains('application/pdf')) {
        // debugPrint('Found PDF option: ${opt.url}');
        if (!allDocumentOptions.contains(opt)) {
          allDocumentOptions.add(opt);
        }
      } else if (opt.format.contains('text/plain')) {
        // debugPrint('Found text option: ${opt.url}');
        if (!allDocumentOptions.contains(opt)) {
          allDocumentOptions.add(opt);
        }
      }
    }

    // Find article content
    MediaOption articleOption = item.options.firstWhere(
      (MediaOption opt) =>
          opt.format.contains('article') || opt.role == 'article',
      orElse: () => const MediaOption(optionId: 'default', format: 'article'),
    );

    // Check if we have HTML content in the options
    String? htmlContent;

    // First, try to find an option with format 'html'
    final MediaOption htmlOption = item.options.firstWhere(
      (MediaOption opt) => opt.format.toLowerCase() == 'html',
      orElse: () => const MediaOption(optionId: 'default', format: 'html'),
    );

    // Check if the option has an html field and is valid
    if (htmlOption.optionId.isNotEmpty &&
        htmlOption.html != null &&
        htmlOption.html!.isNotEmpty) {
      htmlContent = htmlOption.html;
      // debugPrint(
      //     'Found HTML content in option: ${htmlContent?.substring(0, min(50, htmlContent.length))}...');
    } else {
      // debugPrint('No HTML option found for item ${item.id}');
    }

    // If we still don't have HTML content, check if we have it in metadata
    if (htmlContent == null &&
        item.metadata != null &&
        item.metadata!.html != null) {
      htmlContent = item.metadata!.html;
      debugPrint(
          'Found HTML content in metadata: ${htmlContent?.substring(0, min(50, htmlContent.length))}...');
    }

    // If we found HTML content but no article option, create one
    if (articleOption.optionId.isEmpty &&
        htmlContent != null &&
        htmlContent.isNotEmpty) {
      articleOption = MediaOption(
        optionId: 'html_content',
        format: 'article',
        description: htmlContent,
      );
    }

    // Find tweet content
    // debugPrint(
    //     'Checking for tweet options in item ${item.id}, type: ${item.type}');
    // for (final MediaOption opt in item.options) {
    //   debugPrint('Option format: ${opt.format}, role: ${opt.role}');
    // }

    final MediaOption tweetOption = item.options.firstWhere(
      (MediaOption opt) => opt.format.contains('tweet') || opt.role == 'tweet',
      orElse: () => const MediaOption(optionId: 'default', format: 'tweet'),
    );

    // debugPrint(
    //     'Found tweet option: ${tweetOption.format}, optionId: ${tweetOption.optionId}');

    // Extract tweet metadata if available
    String? tweetContent;
    String? tweetAuthor;
    DateTime? tweetDate;
    String? tweetPostLink;
    String? tweetImageUrl;
    String? tweetUserId;
    String? tweetUserPageLink;

    if (tweetOption.optionId != null && tweetOption.optionId.isNotEmpty) {
      // Try to parse tweet metadata
      try {
        // Check for new format with postContent and userInfo
        if (tweetOption.format == 'tweet') {
          // New format
          tweetContent = tweetOption.postContent ?? tweetOption.description;
          tweetPostLink = tweetOption.postLink;
          tweetImageUrl = tweetOption.postImage;

          // Extract user info if available
          if (tweetOption.userInfo != null) {
            final Map<String, dynamic> userInfo = tweetOption.userInfo!;
            tweetAuthor = userInfo['name'] as String?;
            tweetUserId = userInfo['id'] as String?;
            tweetUserPageLink = userInfo['page_link'] as String?;
          } else {
            tweetAuthor = 'Dr. Al Farih';
          }

          // Use createdAt from the item if available
          if (item.createdAt != null) {
            tweetDate = item.createdAt;
          }
        }
        // Fallback to old format
        else if (tweetOption.description != null) {
          final Map<String, dynamic> tweetData =
              tweetOption.description!.contains('{')
                  ? Map<String, dynamic>.from(
                      tweetOption.description! as Map<dynamic, dynamic>)
                  : <String, dynamic>{'content': tweetOption.description};

          tweetContent = tweetData['content'] as String?;
          tweetAuthor = tweetData['author'] as String?;

          if (tweetData.containsKey('date')) {
            tweetDate = DateTime.tryParse(tweetData['date'] as String);
          }
        }
      } catch (e) {
        debugPrint('Error parsing tweet data: $e');
        // If parsing fails, just use the description as content
        tweetContent = tweetOption.description;
        tweetAuthor = 'Dr. Al Farih';
      }
    }

    // For document types, ensure we have a valid URL
    String? docUrl;
    String? pdfUrl;

    // If we found a PDF option, use it for PDF URL
    if (pdfOption.optionId.isNotEmpty &&
        pdfOption.url != null &&
        pdfOption.url!.isNotEmpty) {
      pdfUrl = pdfOption.url;
      // debugPrint('Using PDF URL: $pdfUrl');
    }

    // Use the document option URL as the primary document URL
    docUrl = documentOption.url;

    // For text type, check if we have HTML content first, then prioritize PDF if available
    final MediaType mediaType = MediaType.fromString(item.type);
    final MediaCategory mediaCategory = MediaCategory.fromString(item.category);

    if (mediaType == MediaType.text) {
      // If we have HTML content, we'll use that (handled via metadata)
      if (htmlContent != null) {
        // debugPrint(
        //     'Text item has HTML content, will use that instead of document URL');
        // We'll still set docUrl for fallback, but the HTML content will be used first
      }
      // If we have a PDF URL, use it as the primary document URL
      else if (pdfUrl != null) {
        docUrl = pdfUrl;
        // debugPrint('Text item: Using PDF URL as primary: $docUrl');
      } else if (docUrl != null) {
        // If we have a non-PDF URL, keep it but we'll handle it in the viewer
        final String lowerUrl = docUrl.toLowerCase();
        if (!lowerUrl.endsWith('.pdf')) {
          debugPrint('Text item with non-PDF URL: $docUrl');
        }
      }

      // For items with category "html", ensure we don't use a default PDF
      if (mediaCategory == MediaCategory.html && htmlContent != null) {
        // Clear the document URL to ensure HTML content is used
        docUrl = null;
        // debugPrint(
        //     'Item has category "html" and HTML content, clearing document URL');
      }
    }

    // If no document URL is found, try to find any option with a URL
    if ((docUrl == null || docUrl.isEmpty) &&
        (mediaType == MediaType.pdf ||
            mediaType == MediaType.document ||
            (mediaType == MediaType.text &&
                // Skip this for HTML content or items with category "html"
                !(htmlContent != null ||
                    mediaCategory == MediaCategory.html)))) {
      // Try to find any option with a URL for document types
      for (final MediaOption opt in item.options) {
        if (opt.url != null && opt.url!.isNotEmpty) {
          docUrl = opt.url;
          // debugPrint('Found alternative document URL: $docUrl');
          break;
        }
      }

      // If still no URL, use a default test PDF
      // But only if we don't have HTML content
      if (docUrl == null || docUrl.isEmpty) {
        // Don't add a default PDF URL for items with HTML content or category "html"
        if (!(htmlContent != null || mediaCategory == MediaCategory.html)) {
          docUrl =
              'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';
          // debugPrint('Using default test PDF URL: $docUrl');
        } else {
          debugPrint('Skipping default PDF URL for HTML content');
        }
      }
    }

    // We no longer store document URLs in metadata
    // Instead, we'll use the options array directly for downloadable files

    // We'll create the metadata inline in the return statement

    return MediaUiModel(
      id: item.id,
      title: item.title,
      type: item.type,
      category: item.category,
      thumbnailUrl: item.thumbnailUrl,
      audioUrl: audioOption.url,
      videoUrl: videoOption.url,
      documentUrl: docUrl,
      articleText: articleOption.description ?? item.description,
      tweetContent: tweetContent,
      tweetAuthor: tweetAuthor,
      tweetDate: tweetDate,
      tweetPostLink: tweetPostLink,
      tweetImageUrl: tweetImageUrl,
      tweetUserId: tweetUserId,
      tweetUserPageLink: tweetUserPageLink,
      description: item.description, // Add the description here
      // Include all options for document downloads
      options: item.options,
      // Create a more comprehensive metadata object
      metadata: MediaMetadata(
        description: item.description,
        html: htmlContent,
        // Add additional fields from the item
        additionalInfo: <String, dynamic>{
          'language': item.language,
          'duration': item.durationSeconds,
          'license': item.license,
          'createdAt': item.createdAt.toIso8601String(),
          'updatedAt': item.updatedAt.toIso8601String(),
        },
      ),
    );
  }
}
