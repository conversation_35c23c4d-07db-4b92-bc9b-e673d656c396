// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'category_items_page.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(SelectedMediaNotifier)
const selectedMediaNotifierProvider = SelectedMediaNotifierProvider._();

final class SelectedMediaNotifierProvider
    extends $NotifierProvider<SelectedMediaNotifier, String?> {
  const SelectedMediaNotifierProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'selectedMediaNotifierProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$selectedMediaNotifierHash();

  @$internal
  @override
  SelectedMediaNotifier create() => SelectedMediaNotifier();

  @$internal
  @override
  $NotifierProviderElement<SelectedMediaNotifier, String?> $createElement(
          $ProviderPointer pointer) =>
      $NotifierProviderElement(pointer);

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<String?>(value),
    );
  }
}

String _$selectedMediaNotifierHash() =>
    r'a5bda3e015f4fd58ae997422cb21ce20a2cb1eeb';

abstract class _$SelectedMediaNotifier extends $Notifier<String?> {
  String? build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<String?>;
    final element = ref.element as $ClassProviderElement<AnyNotifier<String?>,
        String?, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
