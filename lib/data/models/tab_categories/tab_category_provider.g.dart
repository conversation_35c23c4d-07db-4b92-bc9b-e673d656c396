// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tab_category_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

/// Provider for main tab categories
@ProviderFor(mainTabCategories)
const mainTabCategoriesProvider = MainTabCategoriesProvider._();

/// Provider for main tab categories
final class MainTabCategoriesProvider extends $FunctionalProvider<
        AsyncValue<List<MainTabCategory>>, FutureOr<List<MainTabCategory>>>
    with
        $FutureModifier<List<MainTabCategory>>,
        $FutureProvider<List<MainTabCategory>> {
  /// Provider for main tab categories
  const MainTabCategoriesProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'mainTabCategoriesProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$mainTabCategoriesHash();

  @$internal
  @override
  $FutureProviderElement<List<MainTabCategory>> $createElement(
          $ProviderPointer pointer) =>
      $FutureProviderElement(pointer);

  @override
  FutureOr<List<MainTabCategory>> create(Ref ref) {
    return mainTabCategories(ref);
  }
}

String _$mainTabCategoriesHash() => r'39b8778f48c3cdcb86f0a045de3c4985be66f878';

/// Provider for filtered media items based on selected category
@ProviderFor(categoryFilteredMedia)
const categoryFilteredMediaProvider = CategoryFilteredMediaFamily._();

/// Provider for filtered media items based on selected category
final class CategoryFilteredMediaProvider
    extends $FunctionalProvider<List<MediaUiModel>, List<MediaUiModel>>
    with $Provider<List<MediaUiModel>> {
  /// Provider for filtered media items based on selected category
  const CategoryFilteredMediaProvider._(
      {required CategoryFilteredMediaFamily super.from,
      required ({
        String mediaType,
        String category,
      })
          super.argument})
      : super(
          retry: null,
          name: r'categoryFilteredMediaProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$categoryFilteredMediaHash();

  @override
  String toString() {
    return r'categoryFilteredMediaProvider'
        ''
        '$argument';
  }

  @$internal
  @override
  $ProviderElement<List<MediaUiModel>> $createElement(
          $ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  List<MediaUiModel> create(Ref ref) {
    final argument = this.argument as ({
      String mediaType,
      String category,
    });
    return categoryFilteredMedia(
      ref,
      mediaType: argument.mediaType,
      category: argument.category,
    );
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<MediaUiModel> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<List<MediaUiModel>>(value),
    );
  }

  @override
  bool operator ==(Object other) {
    return other is CategoryFilteredMediaProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$categoryFilteredMediaHash() =>
    r'c110869511a1766a2bf859d69bc0075589fbc09c';

/// Provider for filtered media items based on selected category
final class CategoryFilteredMediaFamily extends $Family
    with
        $FunctionalFamilyOverride<
            List<MediaUiModel>,
            ({
              String mediaType,
              String category,
            })> {
  const CategoryFilteredMediaFamily._()
      : super(
          retry: null,
          name: r'categoryFilteredMediaProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  /// Provider for filtered media items based on selected category
  CategoryFilteredMediaProvider call({
    required String mediaType,
    required String category,
  }) =>
      CategoryFilteredMediaProvider._(argument: (
        mediaType: mediaType,
        category: category,
      ), from: this);

  @override
  String toString() => r'categoryFilteredMediaProvider';
}

/// Provider for the selected main tab index
@ProviderFor(SelectedMainTab)
const selectedMainTabProvider = SelectedMainTabProvider._();

/// Provider for the selected main tab index
final class SelectedMainTabProvider
    extends $NotifierProvider<SelectedMainTab, int> {
  /// Provider for the selected main tab index
  const SelectedMainTabProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'selectedMainTabProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$selectedMainTabHash();

  @$internal
  @override
  SelectedMainTab create() => SelectedMainTab();

  @$internal
  @override
  $NotifierProviderElement<SelectedMainTab, int> $createElement(
          $ProviderPointer pointer) =>
      $NotifierProviderElement(pointer);

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<int>(value),
    );
  }
}

String _$selectedMainTabHash() => r'74733a8f31408c287c98e4f9ef757bad30fbad05';

abstract class _$SelectedMainTab extends $Notifier<int> {
  int build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<int>;
    final element = ref.element
        as $ClassProviderElement<AnyNotifier<int>, int, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

/// Provider for the selected secondary tab index
@ProviderFor(SelectedSecondaryTab)
const selectedSecondaryTabProvider = SelectedSecondaryTabProvider._();

/// Provider for the selected secondary tab index
final class SelectedSecondaryTabProvider
    extends $NotifierProvider<SelectedSecondaryTab, int> {
  /// Provider for the selected secondary tab index
  const SelectedSecondaryTabProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'selectedSecondaryTabProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$selectedSecondaryTabHash();

  @$internal
  @override
  SelectedSecondaryTab create() => SelectedSecondaryTab();

  @$internal
  @override
  $NotifierProviderElement<SelectedSecondaryTab, int> $createElement(
          $ProviderPointer pointer) =>
      $NotifierProviderElement(pointer);

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<int>(value),
    );
  }
}

String _$selectedSecondaryTabHash() =>
    r'3d064d824947eff210c63dfc760849a955dcb828';

abstract class _$SelectedSecondaryTab extends $Notifier<int> {
  int build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<int>;
    final element = ref.element
        as $ClassProviderElement<AnyNotifier<int>, int, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

/// Provider for the selected media ID in the mini player
@ProviderFor(MiniPlayerMediaId)
const miniPlayerMediaIdProvider = MiniPlayerMediaIdProvider._();

/// Provider for the selected media ID in the mini player
final class MiniPlayerMediaIdProvider
    extends $NotifierProvider<MiniPlayerMediaId, String?> {
  /// Provider for the selected media ID in the mini player
  const MiniPlayerMediaIdProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'miniPlayerMediaIdProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$miniPlayerMediaIdHash();

  @$internal
  @override
  MiniPlayerMediaId create() => MiniPlayerMediaId();

  @$internal
  @override
  $NotifierProviderElement<MiniPlayerMediaId, String?> $createElement(
          $ProviderPointer pointer) =>
      $NotifierProviderElement(pointer);

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<String?>(value),
    );
  }
}

String _$miniPlayerMediaIdHash() => r'dc5dc96f3a3552576c281886d7270b67a24ac031';

abstract class _$MiniPlayerMediaId extends $Notifier<String?> {
  String? build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<String?>;
    final element = ref.element as $ClassProviderElement<AnyNotifier<String?>,
        String?, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
