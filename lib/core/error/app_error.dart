import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_error.freezed.dart';

/// Comprehensive error handling for the application
@freezed
class AppError with _$AppError implements Exception {
  /// Network-related errors
  const factory AppError.network({
    required String message,
    int? statusCode,
    String? details,
    @Default(false) bool isRetryable,
  }) = NetworkError;

  /// Storage/Database errors
  const factory AppError.storage({
    required String message,
    String? details,
    String? operation,
  }) = StorageError;

  /// Validation errors
  const factory AppError.validation({
    required String message,
    Map<String, String>? fieldErrors,
  }) = ValidationError;

  /// Authentication/Authorization errors
  const factory AppError.auth({
    required String message,
    @Default(false) bool requiresReauth,
  }) = AuthError;

  /// Media playback errors
  const factory AppError.media({
    required String message,
    String? mediaType,
    String? mediaId,
  }) = MediaError;

  /// File system errors
  const factory AppError.file({
    required String message,
    String? filePath,
    String? operation,
  }) = FileError;

  /// Permission errors
  const factory AppError.permission({
    required String message,
    String? permission,
  }) = PermissionError;

  /// Unknown/Unexpected errors
  const factory AppError.unknown({
    required String message,
    Object? originalError,
    StackTrace? stackTrace,
  }) = UnknownError;

  /// Timeout errors
  const factory AppError.timeout({
    required String message,
    Duration? duration,
  }) = TimeoutError;

  /// Cache errors
  const factory AppError.cache({
    required String message,
    String? cacheKey,
  }) = CacheError;
}

/// Extension to provide user-friendly error messages
extension AppErrorExtension on AppError {
  /// Get user-friendly error message in Arabic
  String get userMessage {
    return when(
      network: (message, statusCode, details, isRetryable) {
        if (statusCode != null) {
          switch (statusCode) {
            case 400:
              return 'طلب غير صحيح. يرجى المحاولة مرة أخرى.';
            case 401:
              return 'يرجى تسجيل الدخول مرة أخرى.';
            case 403:
              return 'ليس لديك صلاحية للوصول إلى هذا المحتوى.';
            case 404:
              return 'المحتوى المطلوب غير موجود.';
            case 500:
              return 'خطأ في الخادم. يرجى المحاولة لاحقاً.';
            default:
              return 'خطأ في الشبكة. يرجى التحقق من اتصالك بالإنترنت.';
          }
        }
        return 'خطأ في الشبكة. يرجى التحقق من اتصالك بالإنترنت.';
      },
      storage: (message, details, operation) =>
          'خطأ في حفظ البيانات. يرجى المحاولة مرة أخرى.',
      validation: (message, fieldErrors) =>
          'يرجى التحقق من البيانات المدخلة.',
      auth: (message, requiresReauth) =>
          'خطأ في المصادقة. يرجى تسجيل الدخول مرة أخرى.',
      media: (message, mediaType, mediaId) =>
          'خطأ في تشغيل الوسائط. يرجى المحاولة مرة أخرى.',
      file: (message, filePath, operation) =>
          'خطأ في الوصول إلى الملف. يرجى المحاولة مرة أخرى.',
      permission: (message, permission) =>
          'يرجى منح الصلاحيات المطلوبة للتطبيق.',
      timeout: (message, duration) =>
          'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.',
      cache: (message, cacheKey) =>
          'خطأ في التخزين المؤقت. يرجى المحاولة مرة أخرى.',
      unknown: (message, originalError, stackTrace) =>
          'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',
    );
  }

  /// Check if the error is retryable
  bool get isRetryable {
    return when(
      network: (message, statusCode, details, isRetryable) => isRetryable,
      storage: (message, details, operation) => true,
      validation: (message, fieldErrors) => false,
      auth: (message, requiresReauth) => false,
      media: (message, mediaType, mediaId) => true,
      file: (message, filePath, operation) => true,
      permission: (message, permission) => false,
      timeout: (message, duration) => true,
      cache: (message, cacheKey) => true,
      unknown: (message, originalError, stackTrace) => true,
    );
  }

  /// Get error severity level
  ErrorSeverity get severity {
    return when(
      network: (message, statusCode, details, isRetryable) {
        if (statusCode != null && statusCode >= 500) {
          return ErrorSeverity.high;
        }
        return ErrorSeverity.medium;
      },
      storage: (message, details, operation) => ErrorSeverity.high,
      validation: (message, fieldErrors) => ErrorSeverity.low,
      auth: (message, requiresReauth) => ErrorSeverity.medium,
      media: (message, mediaType, mediaId) => ErrorSeverity.medium,
      file: (message, filePath, operation) => ErrorSeverity.medium,
      permission: (message, permission) => ErrorSeverity.medium,
      timeout: (message, duration) => ErrorSeverity.medium,
      cache: (message, cacheKey) => ErrorSeverity.low,
      unknown: (message, originalError, stackTrace) => ErrorSeverity.high,
    );
  }
}

/// Error severity levels
enum ErrorSeverity {
  low,
  medium,
  high,
  critical,
}

/// Helper class to create common errors
class AppErrorFactory {
  /// Create network error from DioException
  static AppError fromDioException(dynamic dioException) {
    // Handle DioException types
    final String message = dioException.message ?? 'Network error occurred';
    final int? statusCode = dioException.response?.statusCode;
    
    return AppError.network(
      message: message,
      statusCode: statusCode,
      details: dioException.response?.data?.toString(),
      isRetryable: _isRetryableStatusCode(statusCode),
    );
  }

  /// Create storage error
  static AppError storageError(String message, {String? operation}) {
    return AppError.storage(
      message: message,
      operation: operation,
    );
  }

  /// Create validation error
  static AppError validationError(String message, {Map<String, String>? fieldErrors}) {
    return AppError.validation(
      message: message,
      fieldErrors: fieldErrors,
    );
  }

  /// Create unknown error
  static AppError unknownError(String message, {Object? originalError, StackTrace? stackTrace}) {
    return AppError.unknown(
      message: message,
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  static bool _isRetryableStatusCode(int? statusCode) {
    if (statusCode == null) return true;
    return statusCode >= 500 || statusCode == 408 || statusCode == 429;
  }
}
