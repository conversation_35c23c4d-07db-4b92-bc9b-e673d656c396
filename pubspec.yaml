name: flutter_riverpod_template
description: A flutter boilerplate project with riverpod 2.0 containing riverpod annotations, freezed, lints, sqflite, easy_translations and more

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 0.0.1+1

environment:
  sdk: ">=3.5.0 <4.0.0"
  flutter: ">=3.29.3"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  path_provider: ^2.1.5
  flutter_displaymode: ^0.6.0
  # Removed easy_localization dependency
  ionicons: ^0.2.2
  cupertino_icons: ^1.0.8
  flutter_riverpod: ^3.0.0-dev.15
  riverpod: ^3.0.0-dev.15
  dio: ^5.8.0+1
  dio_smart_retry: ^5.0.0
  equatable: ^2.0.7
  freezed_annotation: ^3.0.0
  riverpod_annotation: ^3.0.0-dev.15
  retrofit: ^4.4.2
  logger: ^2.5.0
  injectable: ^2.5.0
  get_it: ^8.0.3
  get_storage: ^2.1.1
  gap: ^3.0.1
  network_logger: ^1.0.4
  json_annotation: ^4.9.0
  go_router: ^15.1.2
  fpdart: ^1.1.1
  connectivity_plus: ^6.1.4
  flex_color_scheme: ^8.2.0
  url_launcher: ^6.3.1
  intl: ^0.19.0
  stack_trace: ^1.12.1
  water_drop_nav_bar: ^2.2.0+5
  sqflite: ^2.4.2
  path: ^1.9.1
  flutter_gen: ^5.10.0
  video_player: ^2.9.5
  chewie: ^1.11.3
  just_audio: ^0.10.3
  share_plus: ^11.0.0
  syncfusion_flutter_pdfviewer: ^29.2.4
  flutter_pdfview: ^1.4.0+1
  open_file: ^3.3.2
  webview_flutter: ^4.12.0
  flutter_html: ^3.0.0
  youtube_player_flutter: ^9.1.1
  webview_flutter_plus: ^0.4.18
  uuid: ^4.3.3
  crypto: ^3.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  # The "build_runner" package provides a way to generate files using Dart code
  build_runner: ^2.4.15
  json_serializable: ^6.9.5
  riverpod_generator: ^3.0.0-dev.15
  riverpod_lint: ^3.0.0-dev.15
  retrofit_generator: ^9.2.0
  injectable_generator: ^2.7.0
  freezed: ^3.0.6
  rider: ^0.0.1
  flutter_gen_runner:

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  assets:
    - assets/translations/
    - assets/jsons/
    - assets/fonts/

  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300
        - asset: assets/fonts/Cairo-Regular.ttf
          weight: 400
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/Cairo-Black.ttf
          weight: 900

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
