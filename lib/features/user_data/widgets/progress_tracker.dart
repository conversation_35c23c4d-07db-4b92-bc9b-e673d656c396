import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/user_data_providers.dart';

class ProgressTracker extends ConsumerStatefulWidget {
  const ProgressTracker({
    required this.itemId,
    required this.duration,
    required this.position,
    required this.onPositionChanged,
    this.saveInterval = const Duration(seconds: 5),
    super.key,
  });

  final String itemId;
  final Duration duration;
  final Duration position;
  final Function(Duration) onPositionChanged;
  final Duration saveInterval;

  @override
  ConsumerState<ProgressTracker> createState() => _ProgressTrackerState();
}

class _ProgressTrackerState extends ConsumerState<ProgressTracker> {
  Timer? _saveTimer;
  Duration _lastSavedPosition = Duration.zero;
  bool _initialPositionLoaded = false;

  @override
  void initState() {
    super.initState();
    _setupSaveTimer();
    _loadInitialPosition();
  }

  @override
  void dispose() {
    _saveTimer?.cancel();
    // Save final position when widget is disposed
    _saveCurrentPosition();
    super.dispose();
  }

  void _setupSaveTimer() {
    _saveTimer = Timer.periodic(widget.saveInterval, (_) {
      _saveCurrentPosition();
    });
  }

  Future<void> _loadInitialPosition() async {
    if (_initialPositionLoaded) {
      return;
    }

    final double? savedPositionSeconds =
        await ref.read(itemProgressProvider(widget.itemId).future);

    if (savedPositionSeconds != null && context.mounted) {
      final Duration savedPosition =
          Duration(milliseconds: (savedPositionSeconds * 1000).toInt());

      // Only restore position if it's less than the total duration
      if (savedPosition < widget.duration) {
        _lastSavedPosition = savedPosition;
        widget.onPositionChanged(savedPosition);
      }
    }

    _initialPositionLoaded = true;
  }

  Future<void> _saveCurrentPosition() async {
    // Only save if position has changed significantly (more than 1 second)
    if ((widget.position - _lastSavedPosition).inSeconds.abs() > 1) {
      await ref.read(progressNotifierProvider.notifier).saveProgress(
            itemId: widget.itemId,
            positionSeconds: widget.position.inMilliseconds / 1000,
          );
      _lastSavedPosition = widget.position;
    }
  }

  @override
  Widget build(BuildContext context) {
    // This widget doesn't render anything visible
    return const SizedBox.shrink();
  }
}
