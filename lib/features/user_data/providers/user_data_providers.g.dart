// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_data_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(FavoritesNotifier)
const favoritesNotifierProvider = FavoritesNotifierProvider._();

final class FavoritesNotifierProvider
    extends $AsyncNotifierProvider<FavoritesNotifier, List<FavoriteItem>> {
  const FavoritesNotifierProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'favoritesNotifierProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$favoritesNotifierHash();

  @$internal
  @override
  FavoritesNotifier create() => FavoritesNotifier();

  @$internal
  @override
  $AsyncNotifierProviderElement<FavoritesNotifier, List<FavoriteItem>>
      $createElement($ProviderPointer pointer) =>
          $AsyncNotifierProviderElement(pointer);
}

String _$favoritesNotifierHash() => r'03b3186614963483dc466c11e3b7b1832af25482';

abstract class _$FavoritesNotifier extends $AsyncNotifier<List<FavoriteItem>> {
  FutureOr<List<FavoriteItem>> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<List<FavoriteItem>>>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<AsyncValue<List<FavoriteItem>>>,
        AsyncValue<List<FavoriteItem>>,
        Object?,
        Object?>;
    element.handleValue(ref, created);
  }
}

@ProviderFor(isFavorite)
const isFavoriteProvider = IsFavoriteFamily._();

final class IsFavoriteProvider
    extends $FunctionalProvider<AsyncValue<bool>, FutureOr<bool>>
    with $FutureModifier<bool>, $FutureProvider<bool> {
  const IsFavoriteProvider._(
      {required IsFavoriteFamily super.from, required String super.argument})
      : super(
          retry: null,
          name: r'isFavoriteProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$isFavoriteHash();

  @override
  String toString() {
    return r'isFavoriteProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  $FutureProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $FutureProviderElement(pointer);

  @override
  FutureOr<bool> create(Ref ref) {
    final argument = this.argument as String;
    return isFavorite(
      ref,
      argument,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is IsFavoriteProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$isFavoriteHash() => r'734c87599ba116d12161f01c2e88b79797fe353d';

final class IsFavoriteFamily extends $Family
    with $FunctionalFamilyOverride<FutureOr<bool>, String> {
  const IsFavoriteFamily._()
      : super(
          retry: null,
          name: r'isFavoriteProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  IsFavoriteProvider call(
    String itemId,
  ) =>
      IsFavoriteProvider._(argument: itemId, from: this);

  @override
  String toString() => r'isFavoriteProvider';
}

@ProviderFor(ProgressNotifier)
const progressNotifierProvider = ProgressNotifierProvider._();

final class ProgressNotifierProvider
    extends $AsyncNotifierProvider<ProgressNotifier, List<ProgressItem>> {
  const ProgressNotifierProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'progressNotifierProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$progressNotifierHash();

  @$internal
  @override
  ProgressNotifier create() => ProgressNotifier();

  @$internal
  @override
  $AsyncNotifierProviderElement<ProgressNotifier, List<ProgressItem>>
      $createElement($ProviderPointer pointer) =>
          $AsyncNotifierProviderElement(pointer);
}

String _$progressNotifierHash() => r'99b441cece98bbe010dc9e0087551fbe89751b1b';

abstract class _$ProgressNotifier extends $AsyncNotifier<List<ProgressItem>> {
  FutureOr<List<ProgressItem>> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<List<ProgressItem>>>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<AsyncValue<List<ProgressItem>>>,
        AsyncValue<List<ProgressItem>>,
        Object?,
        Object?>;
    element.handleValue(ref, created);
  }
}

@ProviderFor(itemProgress)
const itemProgressProvider = ItemProgressFamily._();

final class ItemProgressProvider
    extends $FunctionalProvider<AsyncValue<double?>, FutureOr<double?>>
    with $FutureModifier<double?>, $FutureProvider<double?> {
  const ItemProgressProvider._(
      {required ItemProgressFamily super.from, required String super.argument})
      : super(
          retry: null,
          name: r'itemProgressProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$itemProgressHash();

  @override
  String toString() {
    return r'itemProgressProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  $FutureProviderElement<double?> $createElement($ProviderPointer pointer) =>
      $FutureProviderElement(pointer);

  @override
  FutureOr<double?> create(Ref ref) {
    final argument = this.argument as String;
    return itemProgress(
      ref,
      argument,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is ItemProgressProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$itemProgressHash() => r'bc61b2f25693fcb143a897ff98e5effa7d66d9fc';

final class ItemProgressFamily extends $Family
    with $FunctionalFamilyOverride<FutureOr<double?>, String> {
  const ItemProgressFamily._()
      : super(
          retry: null,
          name: r'itemProgressProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  ItemProgressProvider call(
    String itemId,
  ) =>
      ItemProgressProvider._(argument: itemId, from: this);

  @override
  String toString() => r'itemProgressProvider';
}

@ProviderFor(HistoryNotifier)
const historyNotifierProvider = HistoryNotifierProvider._();

final class HistoryNotifierProvider
    extends $AsyncNotifierProvider<HistoryNotifier, List<HistoryItem>> {
  const HistoryNotifierProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'historyNotifierProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$historyNotifierHash();

  @$internal
  @override
  HistoryNotifier create() => HistoryNotifier();

  @$internal
  @override
  $AsyncNotifierProviderElement<HistoryNotifier, List<HistoryItem>>
      $createElement($ProviderPointer pointer) =>
          $AsyncNotifierProviderElement(pointer);
}

String _$historyNotifierHash() => r'484fde5d40951aadcf49b53bc6ce814cb8ca7d2f';

abstract class _$HistoryNotifier extends $AsyncNotifier<List<HistoryItem>> {
  FutureOr<List<HistoryItem>> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<List<HistoryItem>>>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<AsyncValue<List<HistoryItem>>>,
        AsyncValue<List<HistoryItem>>,
        Object?,
        Object?>;
    element.handleValue(ref, created);
  }
}

@ProviderFor(UserDataNotifier)
const userDataNotifierProvider = UserDataNotifierProvider._();

final class UserDataNotifierProvider
    extends $NotifierProvider<UserDataNotifier, int> {
  const UserDataNotifierProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'userDataNotifierProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$userDataNotifierHash();

  @$internal
  @override
  UserDataNotifier create() => UserDataNotifier();

  @$internal
  @override
  $NotifierProviderElement<UserDataNotifier, int> $createElement(
          $ProviderPointer pointer) =>
      $NotifierProviderElement(pointer);

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<int>(value),
    );
  }
}

String _$userDataNotifierHash() => r'43b144d26c0d8c6f51614cc1614c67f5b34cfd2b';

abstract class _$UserDataNotifier extends $Notifier<int> {
  int build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<int>;
    final element = ref.element
        as $ClassProviderElement<AnyNotifier<int>, int, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
