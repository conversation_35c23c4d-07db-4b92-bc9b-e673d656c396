// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'article_reader_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

/// Provider for the article reader state
@ProviderFor(ArticleReaderNotifier)
const articleReaderNotifierProvider = ArticleReaderNotifierProvider._();

/// Provider for the article reader state
final class ArticleReaderNotifierProvider
    extends $NotifierProvider<ArticleReaderNotifier, ArticleReaderState> {
  /// Provider for the article reader state
  const ArticleReaderNotifierProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'articleReaderNotifierProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$articleReaderNotifierHash();

  @$internal
  @override
  ArticleReaderNotifier create() => ArticleReaderNotifier();

  @$internal
  @override
  $NotifierProviderElement<ArticleReaderNotifier, ArticleReaderState>
      $createElement($ProviderPointer pointer) =>
          $NotifierProviderElement(pointer);

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ArticleReaderState value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<ArticleReaderState>(value),
    );
  }
}

String _$articleReaderNotifierHash() =>
    r'b24c3a761ba090ea5604fa41501d4f11f788c52a';

abstract class _$ArticleReaderNotifier extends $Notifier<ArticleReaderState> {
  ArticleReaderState build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<ArticleReaderState>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<ArticleReaderState>, ArticleReaderState, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
