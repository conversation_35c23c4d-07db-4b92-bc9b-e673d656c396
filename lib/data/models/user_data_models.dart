// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_data_models.freezed.dart';
part 'user_data_models.g.dart';

enum SyncStatus {
  pending,
  synced,
}

@freezed
abstract class FavoriteItem with _$FavoriteItem {
  factory FavoriteItem({
    required String id,
    required String userId,
    required String itemId,
    required String type,
    required DateTime createdAt,
    @Default(SyncStatus.pending) SyncStatus syncStatus,
  }) = _FavoriteItem;
  FavoriteItem._();

  factory FavoriteItem.fromJson(Map<String, Object?> json) =>
      _$FavoriteItemFromJson(json);
}

@freezed
abstract class ProgressItem with _$ProgressItem {
  factory ProgressItem({
    required String id,
    required String userId,
    required String itemId,
    required double positionSeconds,
    required DateTime lastUpdated,
    @Default(SyncStatus.pending) SyncStatus syncStatus,
  }) = _ProgressItem;
  ProgressItem._();

  factory ProgressItem.fromJson(Map<String, Object?> json) =>
      _$ProgressItemFromJson(json);
}

@freezed
abstract class HistoryItem with _$HistoryItem {
  factory HistoryItem({
    required String id,
    required String userId,
    required String itemId,
    required String actionType,
    required DateTime timestamp,
    @Default(SyncStatus.pending) SyncStatus syncStatus,
  }) = _HistoryItem;
  HistoryItem._();

  factory HistoryItem.fromJson(Map<String, Object?> json) =>
      _$HistoryItemFromJson(json);
}
