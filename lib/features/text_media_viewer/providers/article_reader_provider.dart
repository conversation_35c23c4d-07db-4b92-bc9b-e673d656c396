import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:webview_flutter/webview_flutter.dart';

part 'article_reader_provider.g.dart';

/// State class for the article reader
class ArticleReaderState {
  const ArticleReaderState({
    this.isLoading = true,
    this.isDownloading = false,
    this.webViewController,
    this.fontSize = 16.0,
    this.isDarkMode = false,
  });
  final bool isLoading;
  final bool isDownloading;
  final WebViewController? webViewController;
  final double fontSize;
  final bool isDarkMode;

  /// Creates a copy of this state with the given fields replaced
  ArticleReaderState copyWith({
    bool? isLoading,
    bool? isDownloading,
    WebViewController? webViewController,
    double? fontSize,
    bool? isDarkMode,
  }) {
    return ArticleReaderState(
      isLoading: isLoading ?? this.isLoading,
      isDownloading: isDownloading ?? this.isDownloading,
      webViewController: webViewController ?? this.webViewController,
      fontSize: fontSize ?? this.fontSize,
      isDarkMode: isDarkMode ?? this.isDarkMode,
    );
  }
}

/// Provider for the article reader state
@riverpod
class ArticleReaderNotifier extends _$ArticleReaderNotifier {
  @override
  ArticleReaderState build() {
    return const ArticleReaderState();
  }

  /// Downloads a document with the specified URL and file type
  Future<void> downloadMedia({String? specificUrl, String? fileType}) async {
    if (specificUrl == null || specificUrl.isEmpty) {
      return;
    }

    try {
      // Set loading state
      state = state.copyWith(isDownloading: true);

      try {
        // Use the platform-specific download mechanism
        // This is typically handled by the OS's download manager
        if (state.webViewController != null) {
          await state.webViewController!.runJavaScript(
            '''
            var a = document.createElement('a');
            a.href = '$specificUrl';
            a.download = '${fileType ?? 'document'}';
            a.click();
            ''',
          );
        }
      } catch (e) {
        debugPrint('Error downloading document: $e');
      } finally {
        try {
          // Reset loading state
          state = state.copyWith(isDownloading: false);
        } catch (e) {
          debugPrint('Error resetting download state: $e');
          // This error is likely due to the widget being disposed
          // We can safely ignore it as the widget is no longer visible
        }
      }
    } catch (e) {
      debugPrint('Error in downloadMedia: $e');
      // This error is likely due to the widget being disposed
      // We can safely ignore it as the widget is no longer visible
    }
  }

  /// Initializes the WebView controller with a URL
  void initializeWithUrl(String url) {
    try {
      final WebViewController controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageStarted: (String url) {
              try {
                setLoading(true);
              } catch (e) {
                debugPrint('Error in onPageStarted: $e');
              }
            },
            onPageFinished: (String url) {
              try {
                setLoading(false);
                applyStyles();
              } catch (e) {
                debugPrint('Error in onPageFinished: $e');
              }
            },
            onWebResourceError: (WebResourceError error) {
              try {
                debugPrint('WebView error: ${error.description}');
                setLoading(false);
              } catch (e) {
                debugPrint('Error handling WebResourceError: $e');
              }
            },
          ),
        )
        ..loadRequest(Uri.parse(url));

      state = state.copyWith(webViewController: controller);
    } catch (e) {
      debugPrint('Error initializing WebView with URL: $e');
      // This error is likely due to the widget being disposed
      // We can safely ignore it as the widget is no longer visible
    }
  }

  /// Initializes the WebView controller with HTML content
  void initializeWithHtml(String htmlContent, {bool isHtml = false}) {
    debugPrint(
        'Initializing WebView with ${isHtml ? 'HTML' : 'plain text'} content');
    debugPrint('Content length: ${htmlContent.length} characters');

    // Preview the first part of the content for debugging
    final int previewLength =
        htmlContent.length > 100 ? 100 : htmlContent.length;
    debugPrint(
        'Content preview: ${htmlContent.substring(0, previewLength)}...');

    // Ensure content is properly wrapped in HTML if it's not already
    String content;
    if (isHtml) {
      // Check if the HTML has proper structure
      if (!htmlContent.trim().toLowerCase().contains('<html') ||
          !htmlContent.trim().toLowerCase().contains('<body')) {
        // It's HTML but missing proper structure, wrap it in a basic HTML document
        content = '''
        <!DOCTYPE html>
        <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              padding: 20px;
              direction: rtl;
              text-align: right;
            }
          </style>
        </head>
        <body>
          ${_sanitizeHtml(htmlContent)}
        </body>
        </html>
        ''';
      } else {
        // It's already properly structured HTML
        content = htmlContent;
      }
    } else {
      // It's plain text, wrap it
      content = _wrapHtmlContent(htmlContent);
    }

    debugPrint('Final HTML structure prepared for WebView');

    // Create and configure the WebViewController
    final WebViewController controller = WebViewController();

    // Configure JavaScript and navigation
    controller.setJavaScriptMode(JavaScriptMode.unrestricted);
    controller.setNavigationDelegate(
      NavigationDelegate(
        onPageStarted: (String url) {
          try {
            debugPrint('WebView page started loading');
            setLoading(true);
          } catch (e) {
            debugPrint('Error in onPageStarted: $e');
            // Ignore errors here as they're likely due to widget disposal
          }
        },
        onPageFinished: (String url) {
          try {
            debugPrint('WebView page finished loading');
            setLoading(false);
            applyStyles();

            // Add extra check to ensure content is visible
            controller.runJavaScript('''
              if (document.body.innerText.trim() === '') {
                document.body.innerHTML = '<h1>Content could not be loaded</h1><p>Please try again later.</p>';
              }
            ''');
          } catch (e) {
            debugPrint('Error in onPageFinished: $e');
            // Ignore errors here as they're likely due to widget disposal
          }
        },
        onWebResourceError: (WebResourceError error) {
          try {
            debugPrint('WebView error: ${error.description}');
            // Set loading to false on critical errors
            if (error.errorType ==
                    WebResourceErrorType.javaScriptExceptionOccurred ||
                error.errorType == WebResourceErrorType.failedSslHandshake ||
                error.errorType == WebResourceErrorType.hostLookup) {
              setLoading(false);
            }
          } catch (e) {
            debugPrint('Error handling WebResourceError: $e');
            // Ignore errors here as they're likely due to widget disposal
          }
        },
      ),
    );

    // Load the HTML content with error handling
    try {
      debugPrint('Loading HTML content into WebView...');
      controller.loadHtmlString(content, baseUrl: 'https://dralfarih.app/');
      debugPrint('HTML content loaded into WebView');
    } catch (e) {
      debugPrint('Error loading HTML content: $e');
      // Try with a simplified version if there's an error
      final String fallbackContent = _createFallbackHtml(htmlContent);
      controller.loadHtmlString(fallbackContent,
          baseUrl: 'https://dralfarih.app/');
      debugPrint('Fallback content loaded instead');
    }

    // Update the state with the new controller
    try {
      state = state.copyWith(webViewController: controller);
      debugPrint('WebViewController set in state');
    } catch (e) {
      debugPrint('Error updating state with WebViewController: $e');
      // This error is likely due to the widget being disposed
      // We can safely ignore it as the widget is no longer visible
    }
  }

  /// Creates a simplified fallback HTML in case the original content causes issues
  String _createFallbackHtml(String originalContent) {
    return '''
      <!DOCTYPE html>
      <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            padding: 20px;
            direction: rtl;
            text-align: right;
          }
        </style>
      </head>
      <body>
        <div>${_sanitizeHtml(originalContent)}</div>
      </body>
      </html>
    ''';
  }

  /// Basic HTML sanitization to handle potential issues
  String _sanitizeHtml(String html) {
    // If it doesn't look like HTML, just return it wrapped in a paragraph
    if (!html.trim().startsWith('<')) {
      return '<p>$html</p>';
    }

    // Very basic sanitization - remove scripts and iframes
    final String sanitized = html
        .replaceAll(
            RegExp(r'<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>',
                caseSensitive: false),
            '')
        .replaceAll(
            RegExp(r'<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>',
                caseSensitive: false),
            '');

    return sanitized;
  }

  /// Wraps plain text content in HTML
  String _wrapHtmlContent(String content) {
    return '''
      <!DOCTYPE html>
      <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            padding: 20px;
            direction: rtl;
            text-align: right;
            font-size: ${state.fontSize}px;
            color: ${state.isDarkMode ? '#ffffff' : '#333333'};
            background-color: ${state.isDarkMode ? '#121212' : '#ffffff'};
          }
          h1, h2, h3 {
            color: ${state.isDarkMode ? '#e1e1e1' : '#222222'};
          }
          a {
            color: ${state.isDarkMode ? '#90caf9' : '#1976d2'};
          }
          img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
          }
        </style>
      </head>
      <body>
        $content
      </body>
      </html>
    ''';
  }

  /// Applies styles to the WebView content
  void applyStyles() {
    try {
      if (state.webViewController == null) {
        return;
      }

      final String jsCode = '''
        document.body.style.fontSize = "${state.fontSize}px";
        document.body.style.color = "${state.isDarkMode ? '#ffffff' : '#333333'}";
        document.body.style.backgroundColor = "${state.isDarkMode ? '#121212' : '#ffffff'}";
        var headings = document.querySelectorAll('h1, h2, h3');
        for (var i = 0; i < headings.length; i++) {
          headings[i].style.color = "${state.isDarkMode ? '#e1e1e1' : '#222222'}";
        }
        var links = document.querySelectorAll('a');
        for (var i = 0; i < links.length; i++) {
          links[i].style.color = "${state.isDarkMode ? '#90caf9' : '#1976d2'}";
        }
      ''';

      state.webViewController!.runJavaScript(jsCode);
    } catch (e) {
      debugPrint('Error applying styles: $e');
      // This error is likely due to the widget being disposed
      // We can safely ignore it as the widget is no longer visible
    }
  }

  /// Increases the font size
  void increaseFontSize() {
    try {
      state = state.copyWith(fontSize: state.fontSize + 2);
      applyStyles();
    } catch (e) {
      debugPrint('Error increasing font size: $e');
      // This error is likely due to the widget being disposed
      // We can safely ignore it as the widget is no longer visible
    }
  }

  /// Decreases the font size
  void decreaseFontSize() {
    try {
      final double newSize = (state.fontSize - 2).clamp(12.0, 32.0);
      state = state.copyWith(fontSize: newSize);
      applyStyles();
    } catch (e) {
      debugPrint('Error decreasing font size: $e');
      // This error is likely due to the widget being disposed
      // We can safely ignore it as the widget is no longer visible
    }
  }

  /// Toggles dark mode
  void toggleDarkMode() {
    try {
      state = state.copyWith(isDarkMode: !state.isDarkMode);
      applyStyles();
    } catch (e) {
      debugPrint('Error toggling dark mode: $e');
      // This error is likely due to the widget being disposed
      // We can safely ignore it as the widget is no longer visible
    }
  }

  /// Sets the loading state
  void setLoading(bool loading) {
    try {
      state = state.copyWith(isLoading: loading);
    } catch (e) {
      debugPrint('Error setting loading state: $e');
      // This error is likely due to the widget being disposed
      // We can safely ignore it as the widget is no longer visible
    }
  }
}
