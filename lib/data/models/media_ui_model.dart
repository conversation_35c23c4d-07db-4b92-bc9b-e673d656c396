import 'package:freezed_annotation/freezed_annotation.dart';

import '../enums/media_type_enum.dart';
import 'media_item.dart';
import 'media_item_converter.dart';
import 'media_metadata.dart';

part 'media_ui_model.freezed.dart';
part 'media_ui_model.g.dart';

@freezed
abstract class MediaUiModel with _$MediaUiModel {
  const factory MediaUiModel({
    required String id,
    required String title,
    required String type,
    required String category,
    String? description,
    String? thumbnailUrl,
    // Media URLs - organized by type
    String? audioUrl,
    String? videoUrl,
    String? documentUrl,
    // Content for different media types
    String? articleText,
    String? htmlContent,
    // Tweet-specific fields
    String? tweetContent,
    String? tweetAuthor,
    DateTime? tweetDate,
    String? tweetPostLink,
    String? tweetImageUrl,
    String? tweetUserId,
    String? tweetUserPageLink,
    // Metadata and options
    @MediaMetadataConverter() MediaMetadata? metadata,
    List<MediaOption>? options,
  }) = _MediaUiModel;

  factory MediaUiModel.fromJson(Map<String, dynamic> json) =>
      _$MediaUiModelFromJson(json);

  factory MediaUiModel.fromDomain(MediaItem item) {
    // Use extension methods for cleaner code
    final MediaOption? audioOption = item.primaryAudioOption;
    final MediaOption? videoOption = item.primaryVideoOption;
    final MediaOption? documentOption = item.primaryDocumentOption;
    final MediaOption? tweetOption = item.tweetOption;
    final MediaOption? htmlOption = item.htmlOption;

    // Extract HTML content
    final String? htmlContent = htmlOption?.html ?? item.metadata?.html;

    // Extract tweet data using extension methods
    String? tweetContent;
    String? tweetAuthor;
    DateTime? tweetDate;
    String? tweetPostLink;
    String? tweetImageUrl;
    String? tweetUserId;
    String? tweetUserPageLink;

    if (tweetOption != null) {
      tweetContent = tweetOption.postContent ?? tweetOption.description;
      tweetPostLink = tweetOption.postLink;
      tweetImageUrl = tweetOption.postImage;
      tweetAuthor = tweetOption.tweetAuthorName ?? 'Dr. Al Farih';
      tweetUserId = tweetOption.tweetAuthorId;
      tweetUserPageLink = tweetOption.tweetAuthorPageLink;
      tweetDate = item.createdAt;
    }

    // Get document URL safely
    final String? documentUrl = documentOption?.url;

    // Get article text
    final String? articleText = item.options
        .where((MediaOption opt) =>
            opt.format.contains('article') || opt.role == 'article')
        .firstOrNull
        ?.description;

    return MediaUiModel(
      id: item.id,
      title: item.title,
      type: item.type,
      category: item.category,
      description: item.description,
      thumbnailUrl: item.thumbnailUrl,
      audioUrl: audioOption?.url,
      videoUrl: videoOption?.url,
      documentUrl: documentUrl,
      articleText: articleText ?? item.description,
      htmlContent: htmlContent,
      tweetContent: tweetContent,
      tweetAuthor: tweetAuthor,
      tweetDate: tweetDate,
      tweetPostLink: tweetPostLink,
      tweetImageUrl: tweetImageUrl,
      tweetUserId: tweetUserId,
      tweetUserPageLink: tweetUserPageLink,
      metadata: MediaMetadata(
        description: item.description,
        html: htmlContent,
        additionalInfo: <String, dynamic>{
          'language': item.language,
          'duration': item.durationSeconds,
          'license': item.license,
          'createdAt': item.createdAt.toIso8601String(),
          'updatedAt': item.updatedAt.toIso8601String(),
        },
      ),
      options: item.options,
    );
  }
}

/// Extension to add helper methods to MediaUiModel
extension MediaUiModelExtensions on MediaUiModel {
  // Type checking helpers
  MediaType get mediaType => MediaType.fromString(type);
  bool get isAudio => mediaType == MediaType.audio;
  bool get isVideo => mediaType == MediaType.video;
  bool get isPdf => mediaType == MediaType.pdf;
  bool get isDocument => mediaType == MediaType.document;
  bool get isText => mediaType == MediaType.text;
  bool get isTweet => mediaType == MediaType.tweet;
  bool get isHtml => mediaType == MediaType.html;

  // Content availability checks
  bool get hasAudioContent => audioUrl != null && audioUrl!.isNotEmpty;
  bool get hasVideoContent => videoUrl != null && videoUrl!.isNotEmpty;
  bool get hasDocumentContent => documentUrl != null && documentUrl!.isNotEmpty;
  bool get hasHtmlContent => htmlContent != null && htmlContent!.isNotEmpty;
  bool get hasTweetContent => tweetContent != null && tweetContent!.isNotEmpty;
  bool get hasArticleText => articleText != null && articleText!.isNotEmpty;

  // Get primary content URL based on type
  String? get primaryContentUrl {
    switch (mediaType) {
      case MediaType.audio:
        return audioUrl;
      case MediaType.video:
        return videoUrl;
      case MediaType.pdf:
      case MediaType.document:
        return documentUrl;
      case MediaType.tweet:
        return tweetPostLink;
      case MediaType.text:
      case MediaType.html:
        return documentUrl ?? audioUrl ?? videoUrl;
      case MediaType.unknown:
        return documentUrl ?? audioUrl ?? videoUrl;
    }
  }

  // Get display content based on type
  String? get displayContent {
    switch (mediaType) {
      case MediaType.tweet:
        return tweetContent;
      case MediaType.text:
      case MediaType.html:
        return htmlContent ?? articleText;
      case MediaType.audio:
      case MediaType.video:
      case MediaType.pdf:
      case MediaType.document:
      case MediaType.unknown:
        return articleText ?? description;
    }
  }

  // Check if media has any playable content
  bool get hasPlayableContent => hasAudioContent || hasVideoContent;

  // Check if media has readable content
  bool get hasReadableContent =>
      hasHtmlContent || hasArticleText || hasTweetContent;

  // Get duration from metadata
  int? get durationSeconds {
    return metadata?.additionalInfo?['duration'] as int?;
  }

  // Get created date from metadata
  DateTime? get createdAt {
    final String? dateStr = metadata?.additionalInfo?['createdAt'] as String?;
    if (dateStr == null) {
      return null;
    }
    return DateTime.tryParse(dateStr);
  }

  // Get formatted duration if available
  String? get formattedDuration {
    final int? duration = durationSeconds;
    if (duration == null || duration <= 0) {
      return null;
    }

    final int minutes = duration ~/ 60;
    final int seconds = duration % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  // Get all available download options
  List<MediaOption> get downloadableOptions {
    return options?.where((MediaOption opt) => opt.hasValidContent).toList() ??
        <MediaOption>[];
  }

  // Get best quality audio option
  MediaOption? get bestAudioOption {
    final List<MediaOption> audioOptions =
        options?.where((MediaOption opt) => opt.isAudio).toList() ??
            <MediaOption>[];
    if (audioOptions.isEmpty) {
      return null;
    }

    audioOptions.sort((MediaOption a, MediaOption b) =>
        b.qualityScore.compareTo(a.qualityScore));
    return audioOptions.first;
  }

  // Get best quality video option
  MediaOption? get bestVideoOption {
    final List<MediaOption> videoOptions =
        options?.where((MediaOption opt) => opt.isVideo).toList() ??
            <MediaOption>[];
    if (videoOptions.isEmpty) {
      return null;
    }

    videoOptions.sort((MediaOption a, MediaOption b) =>
        b.qualityScore.compareTo(a.qualityScore));
    return videoOptions.first;
  }

  // Validation
  bool get isValid =>
      id.isNotEmpty &&
      title.isNotEmpty &&
      (hasPlayableContent || hasReadableContent || hasDocumentContent);

  // Search helper
  bool matchesQuery(String query) {
    final String lowerQuery = query.toLowerCase();
    return title.toLowerCase().contains(lowerQuery) ||
        (description?.toLowerCase().contains(lowerQuery) ?? false) ||
        (articleText?.toLowerCase().contains(lowerQuery) ?? false) ||
        (tweetContent?.toLowerCase().contains(lowerQuery) ?? false);
  }
}
