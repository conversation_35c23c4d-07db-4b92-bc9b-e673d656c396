import 'package:flutter/material.dart';

/// Model class for main tab categories
class MainTabCategory {
  MainTabCategory({
    required this.id,
    required this.title,
    required this.titleAr,
    required this.icon,
    required this.secondaryTabs,
  });
  final String id;
  final String title;
  final String titleAr;
  final IconData icon;
  final List<SecondaryTabCategory> secondaryTabs;
}

/// Model class for secondary tab categories
class SecondaryTabCategory {
  SecondaryTabCategory({
    required this.id,
    required this.title,
    required this.titleAr,
    required this.mediaType,
  });
  final String id;
  final String title;
  final String titleAr;
  final String mediaType;
}
