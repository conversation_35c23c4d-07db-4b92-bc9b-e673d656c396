// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pdf_reader_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

/// Provider for the PDF reader state
@ProviderFor(PdfReaderNotifier)
const pdfReaderNotifierProvider = PdfReaderNotifierProvider._();

/// Provider for the PDF reader state
final class PdfReaderNotifierProvider
    extends $NotifierProvider<PdfReaderNotifier, PdfReaderState> {
  /// Provider for the PDF reader state
  const PdfReaderNotifierProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'pdfReaderNotifierProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$pdfReaderNotifierHash();

  @$internal
  @override
  PdfReaderNotifier create() => PdfReaderNotifier();

  @$internal
  @override
  $NotifierProviderElement<PdfReaderNotifier, PdfReaderState> $createElement(
          $ProviderPointer pointer) =>
      $NotifierProviderElement(pointer);

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(PdfReaderState value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<PdfReaderState>(value),
    );
  }
}

String _$pdfReaderNotifierHash() => r'b3b3564bcba9f82919e926490be994d29508c97d';

abstract class _$PdfReaderNotifier extends $Notifier<PdfReaderState> {
  PdfReaderState build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<PdfReaderState>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<PdfReaderState>, PdfReaderState, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
