import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../data/models/media_item.dart';
import '../../data/models/media_items/media_provider.dart';
import '../../data/models/media_items/media_type_enum.dart';
import '../../data/models/media_ui_model.dart';
import '../../utils/exit_confirmation.dart';
import '../navigation/navigation_provider.dart';
import 'article_reader_screen.dart';
import 'pdf_reader_screen.dart';

class TextMediaRouterScreen extends ConsumerWidget {
  const TextMediaRouterScreen({
    required this.mediaId,
    super.key,
  });

  final String mediaId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ref.watch(mediaUiListProvider).when(
          data: (List<MediaUiModel> items) {
            final MediaUiModel mediaItem = items.firstWhere(
              (MediaUiModel item) => item.id == mediaId,
              orElse: () => throw Exception('Media item not found'),
            );

            if (mediaItem == null) {
              return ExitConfirmationWrapper(
                shouldConfirmExit: () => true, // Always show confirmation
                child: Scaffold(
                  appBar: AppBar(
                    title: const Text('مستند'),
                    leading: IconButton(
                      icon: const Icon(Icons.arrow_back),
                      onPressed: () {
                        // Use the navigation provider to handle back navigation
                        ref.read(navigationProvider).safeGoBack(context);
                      },
                      tooltip: 'رجوع',
                    ),
                  ),
                  body: const Center(child: Text('لم يتم العثور على العنصر')),
                ),
              );
            }

            // Determine which viewer to use based on media type and category
            final MediaType mediaType = MediaType.fromString(mediaItem.type);

            if (mediaType == MediaType.text) {
              // Check if it's a PDF document
              final bool isPdf = _isPdfDocument(mediaItem);

              if (isPdf) {
                return PdfReaderScreen(mediaItem: mediaItem);
              }

              // Check if it has HTML content
              if (mediaItem.category.toLowerCase() == 'html' ||
                  _hasHtmlContent(mediaItem)) {
                debugPrint(
                    'Routing to ArticleReaderScreen for item with HTML content: ${mediaItem.title}');
                debugPrint(
                    'Item ID: ${mediaItem.id}, Category: ${mediaItem.category}');

                // Check for HTML content in options for any item
                if (mediaItem.options != null) {
                  for (final MediaOption option in mediaItem.options!) {
                    if (option.format.toLowerCase() == 'html' &&
                        option.html != null) {
                      debugPrint('Found HTML content in options');
                      break;
                    }
                  }
                }

                return ArticleReaderScreen(mediaItem: mediaItem);
              }

              // Check if it's a book
              if (mediaItem.category.toLowerCase() == 'books') {
                // Check if it's a PDF document first
                if (_isPdfDocument(mediaItem)) {
                  return PdfReaderScreen(mediaItem: mediaItem);
                } else {
                  // For non-PDF books, treat as article content
                  return ArticleReaderScreen(mediaItem: mediaItem);
                }
              }

              // Default to article reader for other text cases
              return ArticleReaderScreen(mediaItem: mediaItem);
            } else {
              // For non-text types, show an error message
              return _buildErrorScreen(
                  context, ref, 'Unsupported media type: ${mediaItem.type}');
            }
          },
          loading: () => ExitConfirmationWrapper(
            shouldConfirmExit: () => true, // Always show confirmation
            child: Scaffold(
              appBar: AppBar(
                title: const Text('جاري التحميل...'),
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () {
                    // Use the navigation provider to handle back navigation
                    ref.read(navigationProvider).safeGoBack(context);
                  },
                  tooltip: 'رجوع',
                ),
              ),
              body: const Center(child: CircularProgressIndicator()),
            ),
          ),
          error: (Object error, StackTrace? stackTrace) =>
              ExitConfirmationWrapper(
            shouldConfirmExit: () => true, // Always show confirmation
            child: Scaffold(
              appBar: AppBar(
                title: const Text('خطأ'),
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () {
                    // Use the navigation provider to handle back navigation
                    ref.read(navigationProvider).safeGoBack(context);
                  },
                  tooltip: 'رجوع',
                ),
              ),
              body: Center(child: Text('خطأ: $error')),
            ),
          ),
        );
  }

  bool _isPdfDocument(MediaUiModel mediaItem) {
    // Check if the document URL ends with .pdf
    if (mediaItem.documentUrl != null &&
        mediaItem.documentUrl!.toLowerCase().endsWith('.pdf')) {
      return true;
    }

    // Check if we have a PDF URL in options
    if (mediaItem.options != null && mediaItem.options!.isNotEmpty) {
      for (final MediaOption option in mediaItem.options!) {
        if (option.url != null && option.url!.isNotEmpty) {
          final String format = option.format.toLowerCase();
          if (format.contains('pdf')) {
            return true;
          }
        }
      }
    }

    // Check if we have options in additionalInfo
    if (mediaItem.metadata != null &&
        mediaItem.metadata!.additionalInfo != null &&
        mediaItem.metadata!.additionalInfo!.containsKey('options')) {
      final List<dynamic>? options =
          mediaItem.metadata!.additionalInfo!['options'] as List<dynamic>?;
      if (options != null) {
        for (final dynamic option in options) {
          if (option is Map<dynamic, dynamic> &&
              (option['format'] == 'application/pdf' ||
                  option['format'].toString().contains('pdf'))) {
            return true;
          }
        }
      }
    }

    return false;
  }

  bool _hasHtmlContent(MediaUiModel mediaItem) {
    debugPrint('Checking if item ${mediaItem.id} has HTML content');

    // Check if the category is 'html'
    if (mediaItem.category.toLowerCase() == 'html') {
      debugPrint('Item ${mediaItem.id} has category "${mediaItem.category}"');
      return true;
    }

    // Check if we have HTML content in metadata
    if (mediaItem.metadata != null && mediaItem.metadata!.html != null) {
      debugPrint('Item ${mediaItem.id} has HTML content in metadata');
      return true;
    }

    // Check if we have HTML content in options
    if (mediaItem.options != null && mediaItem.options!.isNotEmpty) {
      for (final MediaOption option in mediaItem.options!) {
        final String format = option.format.toLowerCase();

        // Check for HTML format
        if (format == 'html' || format.contains('html')) {
          debugPrint(
              'Item ${mediaItem.id} has HTML format in options: $format');
          return true;
        }

        // Check for article format
        if (format == 'article' &&
            option.description != null &&
            option.description!.isNotEmpty) {
          debugPrint('Item ${mediaItem.id} has article format in options');
          return true;
        }

        // Check if option has HTML content
        if (option.html != null && option.html!.isNotEmpty) {
          debugPrint('Item ${mediaItem.id} has HTML content in option');
          return true;
        }
      }
    }

    // Check if we have options in additionalInfo
    if (mediaItem.metadata != null &&
        mediaItem.metadata!.additionalInfo != null &&
        mediaItem.metadata!.additionalInfo!.containsKey('options')) {
      final List<dynamic>? options =
          mediaItem.metadata!.additionalInfo!['options'] as List<dynamic>?;
      if (options != null) {
        for (final dynamic option in options) {
          if (option is Map<dynamic, dynamic>) {
            // Check for HTML format
            if (option['format'] == 'html' ||
                (option['format'] is String &&
                    (option['format'] as String)
                        .toLowerCase()
                        .contains('html'))) {
              debugPrint(
                  'Item ${mediaItem.id} has HTML format in additionalInfo options');
              return true;
            }

            // Check for article format
            if (option['format'] == 'article' ||
                (option['format'] is String &&
                    (option['format'] as String).toLowerCase() == 'article')) {
              debugPrint(
                  'Item ${mediaItem.id} has article format in additionalInfo options');
              return true;
            }

            // Check for HTML content in the option
            if (option['html'] != null &&
                option['html'].toString().isNotEmpty) {
              debugPrint(
                  'Item ${mediaItem.id} has HTML content in additionalInfo options');
              return true;
            }

            // Check for description content in the option (for article format)
            if (option['description'] != null &&
                option['description'].toString().isNotEmpty) {
              debugPrint(
                  'Item ${mediaItem.id} has description content in additionalInfo options');
              return true;
            }
          }
        }
      }
    }

    // Check if description or article text contains HTML markers
    final String? description = mediaItem.metadata?.description;
    final String? articleText = mediaItem.articleText;

    if (description != null && _looksLikeHtml(description)) {
      debugPrint('Item ${mediaItem.id} has HTML-like content in description');
      return true;
    }

    if (articleText != null && _looksLikeHtml(articleText)) {
      debugPrint('Item ${mediaItem.id} has HTML-like content in articleText');
      return true;
    }

    debugPrint('Item ${mediaItem.id} does not have HTML content');
    return false;
  }

  bool _looksLikeHtml(String content) {
    final String lowerContent = content.trim().toLowerCase();
    return lowerContent.startsWith('<') &&
        (lowerContent.contains('</html>') ||
            lowerContent.contains('</body>') ||
            lowerContent.contains('</div>') ||
            lowerContent.contains('</p>') ||
            lowerContent.contains('<html') ||
            lowerContent.contains('<body') ||
            lowerContent.contains('<head'));
  }

  Widget _buildErrorScreen(
      BuildContext context, WidgetRef ref, String message) {
    return ExitConfirmationWrapper(
      shouldConfirmExit: () => true,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('خطأ'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              ref.read(navigationProvider).safeGoBack(context);
            },
            tooltip: 'رجوع',
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                message,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  ref.read(navigationProvider).safeGoBack(context);
                },
                child: const Text('رجوع'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
