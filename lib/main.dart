// ignore_for_file: always_put_control_body_on_new_line

import 'dart:io';

// Removed easy_localization import
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_displaymode/flutter_displaymode.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:get_storage/get_storage.dart';
import 'package:stack_trace/stack_trace.dart' as stack_trace;

import 'data/sqlite/sqlite.dart';
import 'data/sqlite/sqlite_helper.dart';
import 'di/components/service_locator.dart';
import 'my_app.dart';

/// Try using const constructors as much as possible!

/// This class is used to bypass SSL certificate validation
/// CRITICAL: Only use this in development - NEVER in production
class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback = (X509Certificate cert, String host, int port) {
        // ONLY bypass SSL in debug mode for development
        if (kDebugMode) {
          debugPrint(
              '⚠️ WARNING: Bypassing SSL certificate for $host in DEBUG mode');
          return true;
        }
        // In production, always validate certificates
        return false;
      };
  }
}

void main() async {
  /// Initialize packages
  WidgetsFlutterBinding.ensureInitialized();

  // Override HTTP client to handle SSL certificate issues
  HttpOverrides.global = MyHttpOverrides();

  // Removed EasyLocalization initialization
  await GetStorage.init();
  await initSQLite();
  await configureDependencies();
  await setPreferredOrientations();
  getIt<SQLiteHelper>().initSQLite();
  if (!kIsWeb) {
    if (Platform.isAndroid) {
      await FlutterDisplayMode.setHighRefreshRate();
    }
  }

  runApp(
    const ProviderScope(
      child: MyApp(),
    ),
  );

  /// Add this line to get the error stack trace in release mode
  FlutterError.demangleStackTrace = (StackTrace stack) {
    if (stack is stack_trace.Trace) return stack.vmTrace;
    if (stack is stack_trace.Chain) return stack.toTrace().vmTrace;
    return stack;
  };
}
