import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../data/models/user_data_models.dart';
import '../user_data_service.dart';

part 'user_data_providers.g.dart';

final UserDataService _userDataService = UserDataService();

// Extension to handle the Ref to WidgetRef conversion
extension RefExtension on Ref {
  // This method returns null since we're not in a widget context
  // but the UserDataService methods accept nullable WidgetRef
  WidgetRef? get widgetRef => null;
}

// Favorites providers
@riverpod
class FavoritesNotifier extends _$FavoritesNotifier {
  @override
  Future<List<FavoriteItem>> build() async {
    return _userDataService.getFavorites(ref: ref.widgetRef);
  }

  Future<bool> toggleFavorite({
    required String itemId,
    required String type,
  }) async {
    // Check if ref is mounted before updating state
    if (!ref.mounted) {
      return false;
    }

    state = const AsyncValue<List<FavoriteItem>>.loading();

    try {
      final bool isFavorited = await _userDataService.toggleFavorite(
        itemId: itemId,
        type: type,
        ref: ref.widgetRef,
      );

      // Check if ref is still mounted after the async operation
      if (!ref.mounted) {
        return isFavorited;
      }

      // Refresh favorites list
      final List<FavoriteItem> favorites =
          await _userDataService.getFavorites(ref: ref.widgetRef);

      // Check again if ref is still mounted before updating state
      if (ref.mounted) {
        state = AsyncValue<List<FavoriteItem>>.data(favorites);
      }

      return isFavorited;
    } catch (e) {
      // Check if ref is still mounted before updating state with error
      if (ref.mounted) {
        state = AsyncValue<List<FavoriteItem>>.error(e, StackTrace.current);
      }
      return false;
    }
  }

  Future<void> clearFavorites() async {
    // Check if ref is mounted before updating state
    if (!ref.mounted) {
      return;
    }

    state = const AsyncValue<List<FavoriteItem>>.loading();

    try {
      await _userDataService.clearUserData(
        ref: ref.widgetRef,
        clearProgress: false,
        clearHistory: false,
      );

      // Check if ref is still mounted before updating state
      if (ref.mounted) {
        state = const AsyncValue<List<FavoriteItem>>.data(<FavoriteItem>[]);
      }
    } catch (e) {
      // Check if ref is still mounted before updating state with error
      if (ref.mounted) {
        state = AsyncValue<List<FavoriteItem>>.error(e, StackTrace.current);
      }
    }
  }
}

// Check if an item is favorited
@riverpod
Future<bool> isFavorite(Ref ref, String itemId) async {
  return _userDataService.isFavorite(
    itemId: itemId,
    ref: ref.widgetRef,
  );
}

// Progress providers
@riverpod
class ProgressNotifier extends _$ProgressNotifier {
  @override
  Future<List<ProgressItem>> build() async {
    return _userDataService.getAllProgress(ref: ref.widgetRef);
  }

  Future<void> saveProgress({
    required String itemId,
    required double positionSeconds,
  }) async {
    // Check if ref is mounted before proceeding
    if (!ref.mounted) {
      return;
    }

    try {
      await _userDataService.saveProgress(
        itemId: itemId,
        positionSeconds: positionSeconds,
        ref: ref.widgetRef,
      );

      // Check if ref is still mounted after the async operation
      if (!ref.mounted) {
        return;
      }

      // Refresh progress list
      final List<ProgressItem> progress =
          await _userDataService.getAllProgress(ref: ref.widgetRef);

      // Check again if ref is still mounted before updating state
      if (ref.mounted) {
        state = AsyncValue<List<ProgressItem>>.data(progress);
      }
    } catch (e) {
      // Check if ref is still mounted before updating state with error
      if (ref.mounted) {
        state = AsyncValue<List<ProgressItem>>.error(e, StackTrace.current);
      }
    }
  }

  Future<void> clearProgress() async {
    // Check if ref is mounted before updating state
    if (!ref.mounted) {
      return;
    }

    state = const AsyncValue<List<ProgressItem>>.loading();

    try {
      await _userDataService.clearUserData(
        ref: ref.widgetRef,
        clearFavorites: false,
        clearHistory: false,
      );

      // Check if ref is still mounted before updating state
      if (ref.mounted) {
        state = const AsyncValue<List<ProgressItem>>.data(<ProgressItem>[]);
      }
    } catch (e) {
      // Check if ref is still mounted before updating state with error
      if (ref.mounted) {
        state = AsyncValue<List<ProgressItem>>.error(e, StackTrace.current);
      }
    }
  }
}

// Get progress for a specific item
@riverpod
Future<double?> itemProgress(Ref ref, String itemId) async {
  return _userDataService.getProgress(
    itemId: itemId,
    ref: ref.widgetRef,
  );
}

// History providers
@riverpod
class HistoryNotifier extends _$HistoryNotifier {
  @override
  Future<List<HistoryItem>> build() async {
    return _userDataService.getHistory(ref: ref.widgetRef);
  }

  Future<void> addToHistory({
    required String itemId,
    required String actionType,
  }) async {
    // Check if ref is mounted before proceeding
    if (!ref.mounted) {
      return;
    }

    try {
      await _userDataService.addToHistory(
        itemId: itemId,
        actionType: actionType,
        ref: ref.widgetRef,
      );

      // Check if ref is still mounted after the async operation
      if (!ref.mounted) {
        return;
      }

      // Refresh history list
      final List<HistoryItem> history =
          await _userDataService.getHistory(ref: ref.widgetRef);

      // Check again if ref is still mounted before updating state
      if (ref.mounted) {
        state = AsyncValue<List<HistoryItem>>.data(history);
      }
    } catch (e) {
      // Check if ref is still mounted before updating state with error
      if (ref.mounted) {
        state = AsyncValue<List<HistoryItem>>.error(e, StackTrace.current);
      }
    }
  }

  Future<void> clearHistory() async {
    // Check if ref is mounted before updating state
    if (!ref.mounted) {
      return;
    }

    state = const AsyncValue<List<HistoryItem>>.loading();

    try {
      await _userDataService.clearUserData(
        ref: ref.widgetRef,
        clearFavorites: false,
        clearProgress: false,
      );

      // Check if ref is still mounted before updating state
      if (ref.mounted) {
        state = const AsyncValue<List<HistoryItem>>.data(<HistoryItem>[]);
      }
    } catch (e) {
      // Check if ref is still mounted before updating state with error
      if (ref.mounted) {
        state = AsyncValue<List<HistoryItem>>.error(e, StackTrace.current);
      }
    }
  }
}

// All user data operations
@riverpod
class UserDataNotifier extends _$UserDataNotifier {
  @override
  int build() {
    // Just a counter to trigger rebuilds
    return 0;
  }

  Future<void> clearAllUserData() async {
    // Check if ref is mounted before proceeding
    if (!ref.mounted) {
      return;
    }

    try {
      await _userDataService.clearUserData(
        ref: ref.widgetRef,
      );

      // Check if ref is still mounted before updating providers
      if (!ref.mounted) {
        return;
      }

      // Refresh all providers
      ref.invalidate(favoritesNotifierProvider);
      ref.invalidate(progressNotifierProvider);
      ref.invalidate(historyNotifierProvider);

      // Check again if ref is still mounted before updating state
      if (ref.mounted) {
        state = state + 1;
      }
    } catch (e) {
      // Handle error
      debugPrint('Error clearing user data: $e');
    }
  }

  Future<void> transferAnonymousData(String userId) async {
    // Check if ref is mounted before proceeding
    if (!ref.mounted) {
      return;
    }

    try {
      await _userDataService.transferAnonymousData(
        userId: userId,
      );

      // Check if ref is still mounted before updating providers
      if (!ref.mounted) {
        return;
      }

      // Refresh all providers
      ref.invalidate(favoritesNotifierProvider);
      ref.invalidate(progressNotifierProvider);
      ref.invalidate(historyNotifierProvider);

      // Check again if ref is still mounted before updating state
      if (ref.mounted) {
        state = state + 1;
      }
    } catch (e) {
      // Handle error
      debugPrint('Error transferring anonymous data: $e');
    }
  }
}
