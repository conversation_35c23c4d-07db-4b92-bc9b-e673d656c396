import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../data/enums/media_type_enum.dart';
import '../../data/models/media_items/media_player_controller.dart';
import '../../data/models/media_player_state.dart';
import '../../routing/app_router.dart';
import '../user_data/widgets/favorite_button.dart';
import '../user_data/widgets/history_tracker.dart';
import '../user_data/widgets/progress_tracker.dart';
import 'widgets/article_viewer_widget.dart';
import 'widgets/audio_player_widget.dart';
import 'widgets/enhanced_video_player.dart';
import 'widgets/media_controls.dart';
import 'widgets/media_info_panel.dart';

class MediaPlayerScreen extends ConsumerWidget {
  const MediaPlayerScreen({
    required this.mediaId,
    super.key,
  });

  final String mediaId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    try {
      final MediaPlayerState mediaPlayerState =
          ref.watch(mediaPlayerControllerProvider(mediaId));
      final MediaPlayerController mediaPlayerController =
          ref.watch(mediaPlayerControllerProvider(mediaId).notifier);

      return Scaffold(
        appBar: AppBar(
          title: Text(mediaPlayerState.mediaItem?.title ?? 'مشغل الوسائط'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.go(SGRoute.home.route),
            tooltip: 'رجوع',
          ),
          actions: <Widget>[
            // Favorite button
            if (mediaPlayerState.mediaItem != null)
              FavoriteButton(
                itemId: mediaId,
                itemType: mediaPlayerState.mediaItem!.type,
              ),
            // Play downloaded file button

            // Download button
            IconButton(
              icon: mediaPlayerState.isDownloading ?? true
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.download),
              onPressed: mediaPlayerState.isDownloading ?? true
                  ? null
                  : () => mediaPlayerController.downloadMedia(),
              tooltip: 'تحميل الوسائط',
            ),
            // Share button
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: () => mediaPlayerController.shareMedia(),
              tooltip: 'مشاركة الوسائط',
            ),
          ],
        ),
        body: mediaPlayerState.isLoading
            ? const Center(child: CircularProgressIndicator())
            : mediaPlayerState.errorMessage != null
                ? Center(child: Text('خطأ: ${mediaPlayerState.errorMessage}'))
                : HistoryTracker(
                    itemId: mediaId,
                    actionType: 'view',
                    child: Column(
                      children: <Widget>[
                        Expanded(
                          flex: 3,
                          child: _buildMediaPlayer(
                              mediaPlayerState, mediaPlayerController, context),
                        ),
                        // Only show media controls for audio and video, not for PDF files
                        if (MediaType.fromString(
                                    mediaPlayerState.mediaItem?.type) !=
                                MediaType.text &&
                            !(mediaPlayerState.mediaItem?.documentUrl != null &&
                                mediaPlayerState.mediaItem!.documentUrl!
                                    .toLowerCase()
                                    .endsWith('.pdf')))
                          Column(
                            children: <Widget>[
                              // Add progress tracker (invisible widget)
                              if (mediaPlayerState.totalDuration >
                                  Duration.zero)
                                ProgressTracker(
                                  itemId: mediaId,
                                  duration: mediaPlayerState.totalDuration,
                                  position: mediaPlayerState.currentPosition,
                                  onPositionChanged: (Duration position) =>
                                      mediaPlayerController.seekTo(position),
                                ),

                              MediaControls(
                                isPlaying: mediaPlayerState.isPlaying,
                                currentPosition:
                                    mediaPlayerState.currentPosition,
                                totalDuration: mediaPlayerState.totalDuration,
                                volume: mediaPlayerState.volume,
                                playbackSpeed: mediaPlayerState.playbackSpeed,
                                onPlayPause: () => mediaPlayerState.isPlaying
                                    ? mediaPlayerController.pause()
                                    : mediaPlayerController.playMedia(),
                                onSeek: (Duration position) =>
                                    mediaPlayerController.seekTo(position),
                                onVolumeChanged: (double volume) =>
                                    mediaPlayerController.setVolume(volume),
                                onSpeedChanged: (double speed) =>
                                    mediaPlayerController
                                        .setPlaybackSpeed(speed),
                                // Add new features
                                hasDescription: mediaPlayerState
                                        .mediaItem?.metadata?.description !=
                                    null,
                                onShowCaptions: () =>
                                    mediaPlayerController.showCaptions(context),
                                hasNextAudio: MediaType.fromString(
                                        mediaPlayerState.mediaItem?.type) ==
                                    MediaType.audio,
                                onNextAudio: () =>
                                    mediaPlayerController.playNextAudio(),
                              ),
                            ],
                          ),
                        Expanded(
                          flex: 2,
                          child: MediaInfoPanel(
                              mediaItem: mediaPlayerState.mediaItem!),
                        ),
                        if (mediaPlayerState.isDownloading ?? true)
                          Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              children: <Widget>[
                                Text(
                                  'Downloading: ${(mediaPlayerState.downloadProgress! * 100).toStringAsFixed(1)}%',
                                ),
                                const SizedBox(height: 8),
                                LinearProgressIndicator(
                                  value: mediaPlayerState.downloadProgress,
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
      );
    } catch (e) {
      debugPrint('audio player error: $e');
      // If there's an error, show a fallback UI
      return Scaffold(
        appBar: AppBar(
          title: const Text('خطأ في مشغل الوسائط'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.go(SGRoute.home.route),
            tooltip: 'رجوع',
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                'حدث خطأ: $e',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => context.go(SGRoute.home.route),
                child: const Text('رجوع'),
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget _buildMediaPlayer(
    MediaPlayerState mediaPlayerState,
    MediaPlayerController mediaPlayerController,
    BuildContext context,
  ) {
    try {
      // Check if mediaItem is null first
      if (mediaPlayerState.mediaItem == null) {
        return const Center(
          child: Text('Media item not found'),
        );
      }

      final MediaType mediaType =
          MediaType.fromString(mediaPlayerState.mediaItem?.type);
      final String? mediaCategory =
          mediaPlayerState.mediaItem?.category.toLowerCase();
      // debugPrint('Media type: $mediaType, category: $mediaCategory');

      // Check if this is a tweet or document - redirect to appropriate section
      if (mediaType == MediaType.tweet ||
          (mediaType == MediaType.text && mediaCategory == 'tweet')) {
        // For tweets, immediately navigate to the tweet details page
        final String tweetId = mediaPlayerState.mediaItem!.id;
        debugPrint('Tweet detected with ID: $tweetId');

        // Use WidgetsBinding to navigate after the current frame is built
        WidgetsBinding.instance.addPostFrameCallback((_) {
          final String tweetRoute = '${SGRoute.tweets.route}/$tweetId';
          debugPrint('Navigating to tweet details route: $tweetRoute');
          context.go(tweetRoute);
        });

        // Show a loading indicator while the navigation is being set up
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Loading tweet...'),
            ],
          ),
        );
      } else if (mediaType.isDocument ||
          (mediaType == MediaType.text &&
              mediaPlayerState.mediaItem?.documentUrl != null)) {
        // For documents, redirect to document viewer
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              const Icon(Icons.article, size: 64, color: Colors.amber),
              const SizedBox(height: 16),
              Text(
                'Documents are available in the Documents section',
                style: Theme.of(context).textTheme.titleLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                icon: const Icon(Icons.menu_book),
                label: const Text('View Documents'),
                onPressed: () {
                  // Navigate to documents section
                  context.push('/documents');
                },
              ),
            ],
          ),
        );
      } else if (mediaType == MediaType.audio) {
        // Audio player - keep this functionality
        return AudioPlayerWidget(
          thumbnailUrl: mediaPlayerState.mediaItem?.thumbnailUrl,
          isPlaying: mediaPlayerState.isPlaying,
          isBuffering: mediaPlayerState.isBuffering,
        );
      } else if (mediaType == MediaType.video) {
        // Use the enhanced video player for all video types
        return EnhancedVideoPlayer(mediaId: mediaId);
      } else if (mediaType == MediaType.html) {
        // Use the dedicated article viewer with HTML rendering
        return ArticleViewerWidget(mediaId: mediaId);
      }

      return Center(
        child: Text('Unsupported media type: $mediaType'),
      );
    } catch (e) {
      return Center(
        child: Text('Error building media player: $e'),
      );
    }
  }
}
