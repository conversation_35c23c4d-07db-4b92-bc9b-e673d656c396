import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';

import '../../data/models/media_items/media_provider.dart';
import '../../data/models/media_ui_model.dart';

class TweetDetailsScreen extends ConsumerWidget {
  const TweetDetailsScreen({
    required this.tweetId,
    super.key,
  });

  final String tweetId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ref.watch(mediaUiListProvider).when(
          data: (List<MediaUiModel> items) {
            debugPrint('Looking for tweet with ID: $tweetId');

            // Find the tweet by ID
            MediaUiModel? tweet;
            try {
              tweet = items.firstWhere(
                (MediaUiModel item) => item.id == tweetId,
              );
              debugPrint('Found tweet: ${tweet.title}');
            } catch (e) {
              debugPrint('Error finding tweet: $e');
              // If not found, show error
              return Scaffold(
                appBar: AppBar(title: const Text('تغريدة')),
                body: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text('لم يتم العثور على التغريدة',
                          style: TextStyle(fontSize: 18)),
                      const SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: () => context.pop(),
                        child: const Text('العودة'),
                      ),
                    ],
                  ),
                ),
              );
            }

            // The tweet will never be null due to the firstWhere with orElse throwing an exception
            // But we'll keep error handling in the UI for robustness

            return _TweetDetailsView(tweet: tweet);
          },
          loading: () => Scaffold(
            appBar: AppBar(title: const Text('تغريدة')),
            body: const Center(child: CircularProgressIndicator()),
          ),
          error: (Object error, StackTrace? stackTrace) => Scaffold(
            appBar: AppBar(title: const Text('تغريدة')),
            body: Center(child: Text('خطأ: $error')),
          ),
        );
  }
}

class _TweetDetailsView extends StatelessWidget {
  const _TweetDetailsView({
    required this.tweet,
  });

  final MediaUiModel tweet;

  @override
  Widget build(BuildContext context) {
    final String formattedDate = tweet.tweetDate != null
        ? DateFormat('h:mm a · MMM d, yyyy').format(tweet.tweetDate!)
        : '';

    return Scaffold(
      appBar: AppBar(
        title: const Text('Tweet'),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              final String shareText =
                  '${tweet.tweetContent ?? tweet.title} --- مشاركة تطبيق الشيخ الفريح رابط التحميل: https://bit.ly/3C30uRS ساهم في نشر التطبيق فالدال على الخير كفاعله';
              SharePlus.instance.share(ShareParams(text: shareText));
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // Tweet header
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // Author avatar
                  CircleAvatar(
                    radius: 24,
                    backgroundImage: tweet.thumbnailUrl != null
                        ? NetworkImage(tweet.thumbnailUrl!)
                        : null,
                    child: tweet.thumbnailUrl == null
                        ? const Icon(Icons.person)
                        : null,
                  ),
                  const SizedBox(width: 12),
                  // Author info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Row(
                          children: <Widget>[
                            Text(
                              tweet.tweetAuthor ?? 'د. الفريح',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Icon(
                              Icons.verified,
                              color: Colors.blue[400],
                              size: 16,
                            ),
                          ],
                        ),
                        Text(
                          '@${(tweet.tweetAuthor ?? 'dralfarih').replaceAll(' ', '_').toLowerCase()}',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // More options
                  IconButton(
                    icon: const Icon(Icons.more_horiz),
                    onPressed: () {
                      // Show options menu
                      showModalBottomSheet(
                        context: context,
                        builder: (BuildContext context) {
                          return SafeArea(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: <Widget>[
                                ListTile(
                                  leading: const Icon(Icons.share),
                                  title: const Text('Share Tweet'),
                                  onTap: () {
                                    Navigator.pop(context);
                                    final String shareText =
                                        '${tweet.tweetContent ?? tweet.title} --- مشاركة تطبيق الشيخ الفريح رابط التحميل: https://bit.ly/3C30uRS ساهم في نشر التطبيق فالدال على الخير كفاعله';
                                    SharePlus.instance
                                        .share(ShareParams(text: shareText));
                                  },
                                ),
                                if (tweet.audioUrl != null)
                                  ListTile(
                                    leading: const Icon(Icons.audiotrack),
                                    title: const Text('Play Audio'),
                                    onTap: () {
                                      Navigator.pop(context);
                                      // Navigate to audio player instead
                                      context.push('/mediaPlayer/${tweet.id}');
                                    },
                                  ),
                              ],
                            ),
                          );
                        },
                      );
                    },
                  ),
                ],
              ),
            ),

            // Tweet content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                tweet.tweetContent ??
                    tweet.metadata?.description ??
                    tweet.title,
                style: const TextStyle(fontSize: 18),
              ),
            ),

            // Tweet image if available
            if (tweet.tweetImageUrl != null ||
                (tweet.thumbnailUrl != null && tweet.type != 'audio'))
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: Image.network(
                    tweet.tweetImageUrl ?? tweet.thumbnailUrl!,
                    fit: BoxFit.cover,
                    errorBuilder: (_, __, ___) => const SizedBox.shrink(),
                  ),
                ),
              ),

            // Tweet date
            if (formattedDate.isNotEmpty)
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                child: Text(
                  formattedDate,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ),

            // Divider
            Divider(color: Colors.grey[300]),

            // Tweet stats
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: Row(
                children: <Widget>[
                  _TweetStat(
                    count: tweet.metadata?.retweetCount?.toString() ?? '0',
                    label: 'Retweets',
                  ),
                  const SizedBox(width: 24),
                  _TweetStat(
                    count: tweet.metadata?.likeCount?.toString() ?? '0',
                    label: 'Likes',
                  ),
                ],
              ),
            ),

            Divider(color: Colors.grey[300]),

            // Tweet actions
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: <Widget>[
                  _TweetAction(
                    icon: Icons.chat_bubble_outline,
                    label: 'Comment',
                    onTap: () {},
                  ),
                  _TweetAction(
                    icon: Icons.repeat,
                    label: 'Retweet',
                    onTap: () {},
                  ),
                  _TweetAction(
                    icon: Icons.favorite_border,
                    label: 'Like',
                    onTap: () {},
                  ),
                  _TweetAction(
                    icon: Icons.share_outlined,
                    label: 'Share',
                    onTap: () {
                      final String shareText =
                          '${tweet.tweetContent ?? tweet.title} --- مشاركة تطبيق الشيخ الفريح رابط التحميل: https://bit.ly/3C30uRS ساهم في نشر التطبيق فالدال على الخير كفاعله';
                      SharePlus.instance.share(ShareParams(text: shareText));
                    },
                  ),
                ],
              ),
            ),

            // Audio player if available
            if (tweet.audioUrl != null)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('Play Audio'),
                  style: ElevatedButton.styleFrom(
                    minimumSize: const Size.fromHeight(50),
                  ),
                  onPressed: () {
                    // Navigate to audio player
                    context.push('/mediaPlayer/${tweet.id}');
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class _TweetStat extends StatelessWidget {
  const _TweetStat({
    required this.count,
    required this.label,
  });

  final String count;
  final String label;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        Text(
          count,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
      ],
    );
  }
}

class _TweetAction extends StatelessWidget {
  const _TweetAction({
    required this.icon,
    required this.label,
    required this.onTap,
  });

  final IconData icon;
  final String label;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Icon(icon, size: 20, color: Colors.grey[700]),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[700],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
