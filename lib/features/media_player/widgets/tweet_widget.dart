import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../data/models/media_items/media_player_controller.dart';
import '../../../data/models/media_player_state.dart';

class TweetWidget extends ConsumerWidget {
  const TweetWidget({
    required this.mediaId,
    super.key,
  });

  final String mediaId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final MediaPlayerState mediaPlayerState =
        ref.watch(mediaPlayerControllerProvider(mediaId));
    final MediaPlayerController controller =
        ref.read(mediaPlayerControllerProvider(mediaId).notifier);

    if (mediaPlayerState.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (mediaPlayerState.mediaItem?.tweetContent == null) {
      return const Center(
        child: Text('No tweet content available'),
      );
    }

    final String? tweetContent = mediaPlayerState.mediaItem!.tweetContent;
    final String tweetAuthor =
        mediaPlayerState.mediaItem!.tweetAuthor ?? 'Unknown';
    final DateTime? tweetDate = mediaPlayerState.mediaItem!.tweetDate;
    final bool isLiked = mediaPlayerState.isLiked ?? false;
    final int likeCount = mediaPlayerState.likeCount ?? 0;
    final int retweetCount = mediaPlayerState.retweetCount ?? 0;

    // Format the date if available
    String formattedDate = '';
    if (tweetDate != null) {
      formattedDate = DateFormat('h:mm a · MMM d, yyyy').format(tweetDate);
    }

    return Center(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500),
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          boxShadow: <BoxShadow>[
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // Tweet header
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // Profile picture
                  CircleAvatar(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    child: Text(
                      tweetAuthor.isNotEmpty
                          ? tweetAuthor[0].toUpperCase()
                          : '?',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Author info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          tweetAuthor,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        if (formattedDate.isNotEmpty)
                          Text(
                            formattedDate,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                      ],
                    ),
                  ),

                  // Twitter logo
                  Icon(
                    Icons.verified,
                    color: Colors.blue[400],
                    size: 20,
                  ),
                ],
              ),
            ),

            // Tweet content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                tweetContent!,
                style: const TextStyle(
                  fontSize: 16,
                  height: 1.4,
                ),
              ),
            ),

            // Tweet actions
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  // Comment
                  Row(
                    children: <Widget>[
                      IconButton(
                        icon: const Icon(Icons.chat_bubble_outline),
                        onPressed: () {
                          // Show a snackbar since we're not implementing comments
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content:
                                  Text('Comments not implemented in this demo'),
                            ),
                          );
                        },
                        iconSize: 20,
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                      const SizedBox(width: 4),
                      const Text('0'),
                    ],
                  ),

                  // Retweet
                  Row(
                    children: <Widget>[
                      IconButton(
                        icon: const Icon(Icons.repeat),
                        onPressed: () => controller.retweet(),
                        iconSize: 20,
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                      const SizedBox(width: 4),
                      Text('$retweetCount'),
                    ],
                  ),

                  // Like
                  Row(
                    children: <Widget>[
                      IconButton(
                        icon: Icon(
                          isLiked ? Icons.favorite : Icons.favorite_border,
                          color: isLiked ? Colors.red : null,
                        ),
                        onPressed: () => controller.toggleLike(),
                        iconSize: 20,
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                      const SizedBox(width: 4),
                      Text('$likeCount'),
                    ],
                  ),

                  // Share
                  IconButton(
                    icon: const Icon(Icons.share),
                    onPressed: () => controller.shareContent(),
                    iconSize: 20,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
