import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../data/models/media_ui_model.dart';
import '../../../data/models/user_data_models.dart';
import '../../../routing/app_router.dart';
import '../../../utils/context_extensions.dart';
import '../../../utils/icons_changer.dart';
import '../../user_data/providers/media_providers.dart';
import '../../user_data/providers/user_data_providers.dart';

class HistoryScreen extends ConsumerWidget {
  const HistoryScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<List<HistoryItem>> historyAsync =
        ref.watch(historyNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('سجل المشاهدة'),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.delete_outline),
            onPressed: () => _confirmClearHistory(context, ref),
            tooltip: 'حذف سجل المشاهدة',
          ),
        ],
      ),
      backgroundColor: context.colorScheme.surface,
      body: historyAsync.when(
        data: (List<HistoryItem> history) {
          if (history.isEmpty) {
            return _buildEmptyState(context);
          }
          return _buildHistoryList(context, ref, history);
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (Object error, StackTrace stackTrace) => Center(
          child: Text('حدث خطأ: $error'),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Icons.history,
            size: 64,
            color: context.colorScheme.secondary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'سجل المشاهدة فارغ',
            style: context.textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر هنا العناصر التي قمت بمشاهدتها',
            style: context.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryList(
    BuildContext context,
    WidgetRef ref,
    List<HistoryItem> history,
  ) {
    // Sort history by timestamp (newest first)
    final List<HistoryItem> sortedHistory = List<HistoryItem>.from(history)
      ..sort(
          (HistoryItem a, HistoryItem b) => b.timestamp.compareTo(a.timestamp));

    return ListView.builder(
      itemCount: sortedHistory.length,
      padding: const EdgeInsets.all(16),
      itemBuilder: (BuildContext context, int index) {
        final HistoryItem historyItem = sortedHistory[index];

        // Get the media item details using the itemId
        final AsyncValue<MediaUiModel?> mediaItemAsync =
            ref.watch(mediaItemProvider(historyItem.itemId));

        return mediaItemAsync.when(
          data: (MediaUiModel? mediaItem) {
            if (mediaItem == null) {
              return _buildUnavailableItem(context, historyItem);
            }
            return _buildHistoryItem(context, ref, historyItem, mediaItem);
          },
          loading: () => const Card(
            margin: EdgeInsets.only(bottom: 16),
            child: ListTile(
              title: Text('جاري التحميل...'),
              leading: CircularProgressIndicator(),
            ),
          ),
          error: (Object error, StackTrace stackTrace) => const Card(
            margin: EdgeInsets.only(bottom: 16),
            child: ListTile(
              title: Text('عنصر غير متوفر'),
              subtitle: Text('تعذر تحميل بيانات العنصر'),
              leading: Icon(Icons.error_outline),
            ),
          ),
        );
      },
    );
  }

  Widget _buildUnavailableItem(BuildContext context, HistoryItem historyItem) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ListTile(
        title: Text('عنصر غير متوفر (${historyItem.itemId})'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text('نوع الإجراء: ${_getActionTypeText(historyItem.actionType)}'),
            Text(
              'تاريخ المشاهدة: ${_formatDateTime(historyItem.timestamp)}',
              style: context.textTheme.bodySmall,
            ),
          ],
        ),
        isThreeLine: true,
        leading: const Icon(Icons.help_outline),
        // Navigate to item details page
        onTap: () => _navigateToItemDetails(context, historyItem.itemId),
      ),
    );
  }

  Widget _buildHistoryItem(
    BuildContext context,
    WidgetRef ref,
    HistoryItem historyItem,
    MediaUiModel mediaItem,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ListTile(
        title: Text(mediaItem.title),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
                '${mediaItem.category} - ${_getActionTypeText(historyItem.actionType)}'),
            Text(
              'تاريخ المشاهدة: ${_formatDateTime(historyItem.timestamp)}',
              style: context.textTheme.bodySmall,
            ),
          ],
        ),
        isThreeLine: true,
        leading: mediaItem.thumbnailUrl != null
            ? ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: Image.network(
                  mediaItem.thumbnailUrl!,
                  width: 56,
                  height: 56,
                  fit: BoxFit.cover,
                  errorBuilder: (BuildContext context, Object error,
                      StackTrace? stackTrace) {
                    return const Icon(Icons.broken_image, size: 56);
                  },
                ),
              )
            : Icon(
                getIconForType(mediaItem.type),
                size: 56,
              ),
        trailing: IconButton(
          icon: const Icon(Icons.play_arrow),
          onPressed: () => _playMedia(context, mediaItem),
          tooltip: 'تشغيل',
        ),
        onTap: () => _playMedia(context, mediaItem),
      ),
    );
  }

  void _playMedia(BuildContext context, MediaUiModel mediaItem) {
    // Always navigate to the MediaPlayerScreen regardless of media type
    context.push('${SGRoute.mediaPlayer.route}/${mediaItem.id}');
  }

  // Navigate to item details page
  void _navigateToItemDetails(BuildContext context, String itemId) {
    // Always navigate to the MediaPlayerScreen
    context.push('${SGRoute.mediaPlayer.route}/$itemId');
  }

  Future<void> _confirmClearHistory(BuildContext context, WidgetRef ref) async {
    final bool confirm = await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('حذف سجل المشاهدة'),
              content: const Text('هل أنت متأكد أنك تريد حذف سجل المشاهدة؟'),
              actions: <Widget>[
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('إلغاء'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  style: TextButton.styleFrom(foregroundColor: Colors.red),
                  child: const Text('حذف'),
                ),
              ],
            );
          },
        ) ??
        false;

    if (confirm && context.mounted) {
      await ref.read(historyNotifierProvider.notifier).clearHistory();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حذف سجل المشاهدة بنجاح')),
        );
      }
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month}/${dateTime.day} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _getActionTypeText(String actionType) {
    switch (actionType.toLowerCase()) {
      case 'view':
        return 'مشاهدة';
      case 'play':
        return 'تشغيل';
      case 'read':
        return 'قراءة';
      case 'download':
        return 'تنزيل';
      default:
        return actionType;
    }
  }
}
