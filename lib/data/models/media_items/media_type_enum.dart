import 'package:freezed_annotation/freezed_annotation.dart';

/// Enum representing different media types in the application
enum MediaType {
  @JsonValue('audio')
  audio,

  @JsonValue('video')
  video,

  @JsonValue('text')
  text,

  @JsonValue('pdf')
  pdf,

  @JsonValue('document')
  document,

  @JsonValue('tweet')
  tweet,

  @JsonValue('html')
  html,

  @JsonValue('unknown')
  unknown;

  /// Convert a string to MediaType enum
  static MediaType fromString(String? value) {
    if (value == null) {
      return MediaType.unknown;
    }

    final String normalizedValue = value.toLowerCase();

    return MediaType.values.firstWhere(
      (MediaType type) => type.toString().split('.').last == normalizedValue,
      orElse: () => MediaType.unknown,
    );
  }

  /// Check if the media type is audio or video
  bool get isAudioOrVideo => this == MediaType.audio || this == MediaType.video;

  /// Check if the media type is a document (pdf or document)
  bool get isDocument => this == MediaType.pdf || this == MediaType.document;

  /// Check if the media type is a tweet
  bool get isTweet => this == MediaType.tweet;

  /// Check if the media type is text with HTML content
  bool get isHtmlText => this == MediaType.html || this == MediaType.text;
}

/// Enum representing different media categories in the application
enum MediaCategory {
  @JsonValue('tweet')
  tweet,

  @JsonValue('html')
  html,

  @JsonValue('unknown')
  unknown;

  /// Convert a string to MediaCategory enum
  static MediaCategory fromString(String? value) {
    if (value == null) {
      return MediaCategory.unknown;
    }

    final String normalizedValue = value.toLowerCase();

    return MediaCategory.values.firstWhere(
      (MediaCategory category) =>
          category.toString().split('.').last == normalizedValue,
      orElse: () => MediaCategory.unknown,
    );
  }
}
