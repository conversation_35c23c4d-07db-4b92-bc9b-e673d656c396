import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../data/models/media_items/media_navigation_helper.dart';
import '../../data/models/media_items/media_provider.dart';
import '../../data/models/media_items/media_type_enum.dart';
import '../../data/models/media_ui_model.dart';
import 'providers/search_provider.dart';

class SearchScreen extends ConsumerStatefulWidget {
  const SearchScreen({super.key});

  @override
  SearchScreenState createState() => SearchScreenState();
}

class SearchScreenState extends ConsumerState<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final List<String> _mediaTypes = <String>[
    'الكل',
    'صوت',
    'فيديو',
    'تغريدة',
    'نص'
  ];

  @override
  void dispose() {
    _searchController.dispose();
    // Clear the search query when leaving the search screen
    ref.read(mediaSearchProvider.notifier).reset();
    // Force refresh of media data
    ref.invalidate(mediaListProvider);
    ref.invalidate(mediaUiListProvider);
    debugPrint(
        'SearchScreen: Reset search query and refreshed media data on dispose');
    super.dispose();
  }

  @override
  void initState() {
    super.initState();

    // Set up the controller listener
    _searchController.addListener(() {
      final SearchState searchState = ref.read(searchNotifierProvider);
      if (_searchController.text != searchState.searchQuery) {
        ref
            .read(searchNotifierProvider.notifier)
            .updateSearchQuery(_searchController.text);
        // Update the global search provider
        ref.read(mediaSearchProvider.notifier).value = _searchController.text;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final SearchState searchState = ref.watch(searchNotifierProvider);

    // Initialize the controller with the current search query
    if (_searchController.text != searchState.searchQuery) {
      _searchController.text = searchState.searchQuery;
    }

    return WillPopScope(
        onWillPop: () async {
          // Reset search query when navigating back
          ref.read(mediaSearchProvider.notifier).reset();
          ref.read(searchNotifierProvider.notifier).reset();
          // Force refresh of media data - but avoid circular dependencies
          // We don't need to invalidate these providers as they will rebuild when needed
          debugPrint('SearchScreen: Reset search query on back navigation');
          return true; // Allow the pop to happen
        },
        child: Scaffold(
          appBar: AppBar(
            title: const Text('بحث'),
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(48),
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'ابحث عن المحتوى...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    filled: true,
                    fillColor: Theme.of(context).colorScheme.surface,
                  ),
                ),
              ),
            ),
          ),
          body: Column(
            children: <Widget>[
              // Search results
              Expanded(
                child: _buildSearchResults(context),
              ),
            ],
          ),
        ));
  }

  Widget _buildSearchResults(BuildContext context) {
    final SearchState searchState = ref.watch(searchNotifierProvider);
    final List<MediaUiModel> mediaItems = ref.watch(filteredMediaProvider);

    if (searchState.searchQuery.isEmpty) {
      return const Center(
        child: Text('أدخل كلمة للبحث عن المحتوى'),
      );
    }

    // Determine which media types have results
    final Set<String> availableTypes = <String>{'الكل'};
    final Map<String, String> englishToArabicMap = <String, String>{
      'audio': 'صوت',
      'video': 'فيديو',
      'tweet': 'تغريدة',
      'text': 'نص',
    };

    // Add available types based on search results
    for (final MediaUiModel item in mediaItems) {
      final String arabicType =
          englishToArabicMap[item.type.toLowerCase()] ?? item.type;
      availableTypes.add(arabicType);
    }

    // Filter by selected type if needed
    final List<MediaUiModel> filteredItems =
        searchState.selectedType == null || searchState.selectedType == 'الكل'
            ? mediaItems
            : mediaItems.where((MediaUiModel item) {
                // Map Arabic type names to English for filtering
                String englishType;
                switch (searchState.selectedType) {
                  case 'صوت':
                    englishType = 'audio';
                    break;
                  case 'فيديو':
                    englishType = 'video';
                    break;
                  case 'تغريدة':
                    englishType = 'tweet';
                    break;
                  case 'نص':
                    englishType = 'text';
                    break;
                  default:
                    englishType = searchState.selectedType!.toLowerCase();
                }
                return MediaType.fromString(item.type) ==
                    MediaType.fromString(englishType);
              }).toList();

    // Build the filter chips for available types
    final Widget filterChips = SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        children: _mediaTypes
            .where((String type) => availableTypes.contains(type))
            .map((String type) {
          final bool isSelected = searchState.selectedType == type ||
              (searchState.selectedType == null && type == 'الكل');
          return Padding(
            padding: const EdgeInsets.only(left: 8.0),
            child: FilterChip(
              label: Text(type),
              selected: isSelected,
              onSelected: (bool selected) {
                ref
                    .read(searchNotifierProvider.notifier)
                    .setSelectedType(selected ? type : null);
              },
            ),
          );
        }).toList(),
      ),
    );

    if (filteredItems.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          filterChips,
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  const Icon(Icons.search_off, size: 64),
                  const SizedBox(height: 16),
                  Text('لا توجد نتائج لـ "${searchState.searchQuery}"'),
                ],
              ),
            ),
          ),
        ],
      );
    }

    // Group items by type for better organization
    final Map<MediaType, List<MediaUiModel>> groupedItems =
        <MediaType, List<MediaUiModel>>{};
    for (final MediaUiModel item in filteredItems) {
      final MediaType type = MediaType.fromString(item.type);
      if (!groupedItems.containsKey(type)) {
        groupedItems[type] = <MediaUiModel>[];
      }
      groupedItems[type]!.add(item);
    }

    // Build the list with section headers
    return Column(
      children: <Widget>[
        // Show filter chips at the top
        filterChips,

        // Show search results
        Expanded(
          child: ListView.builder(
            itemCount:
                groupedItems.keys.length * 2, // Double for headers and sections
            itemBuilder: (BuildContext context, int index) {
              // Even indices are headers, odd indices are lists
              if (index.isEven) {
                final int headerIndex = index ~/ 2;
                final MediaType type = groupedItems.keys.elementAt(headerIndex);
                return Padding(
                  padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 8.0),
                  child: Text(
                    'نتائج ${_getArabicTypeName(type.toString().split('.').last)}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                );
              } else {
                final int sectionIndex = index ~/ 2;
                final MediaType type =
                    groupedItems.keys.elementAt(sectionIndex);
                final List<MediaUiModel> sectionItems = groupedItems[type]!;

                return ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: sectionItems.length,
                  itemBuilder: (BuildContext context, int itemIndex) {
                    final MediaUiModel item = sectionItems[itemIndex];
                    return _buildMediaItem(context, item);
                  },
                );
              }
            },
          ),
        ),
      ],
    );
  }

  // Helper method to convert English type names to Arabic
  String _getArabicTypeName(String englishType) {
    switch (englishType.toLowerCase()) {
      case 'audio':
        return 'صوت';
      case 'video':
        return 'فيديو';
      case 'tweet':
        return 'تغريدة';
      case 'text':
        return 'نص';
      default:
        return englishType;
    }
  }

  Widget _buildMediaItem(BuildContext context, MediaUiModel item) {
    // Helper method to get icon for media type
    IconData getIconForMediaType(String type) {
      switch (type.toLowerCase()) {
        case 'audio':
          return Icons.audiotrack;
        case 'video':
          return Icons.videocam;
        case 'text':
          return Icons.article;
        case 'tweet':
          return Icons.format_quote;
        default:
          return Icons.insert_drive_file;
      }
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
      child: ListTile(
        leading: item.thumbnailUrl != null
            ? Image.network(
                item.thumbnailUrl!,
                width: 50,
                height: 50,
                fit: BoxFit.cover,
                errorBuilder: (_, __, ___) => Icon(
                  getIconForMediaType(item.type),
                  size: 40,
                ),
              )
            : Icon(
                getIconForMediaType(item.type),
                size: 40,
              ),
        title: Text(item.title),
        subtitle: Text('${_getArabicTypeName(item.type)} • ${item.category}'),
        onTap: () {
          // Navigate to the media player
          const MediaNavigationHelper mediaHelper = MediaNavigationHelper();
          mediaHelper.navigateToMediaPlayer(context, item);
        },
      ),
    );
  }
}
