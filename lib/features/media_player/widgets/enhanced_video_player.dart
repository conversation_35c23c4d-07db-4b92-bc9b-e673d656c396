import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:video_player/video_player.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

import '../../../data/models/media_items/media_player_controller.dart';
import '../../../data/models/media_player_state.dart';
import '../providers/video_player_provider.dart';

class EnhancedVideoPlayer extends ConsumerStatefulWidget {
  const EnhancedVideoPlayer({
    required this.mediaId,
    super.key,
  });

  final String mediaId;

  @override
  ConsumerState<EnhancedVideoPlayer> createState() =>
      _EnhancedVideoPlayerState();
}

class _EnhancedVideoPlayerState extends ConsumerState<EnhancedVideoPlayer> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializePlayer();
    });
  }

  @override
  void dispose() {
    // Clean up resources
    ref.read(videoPlayerNotifierProvider.notifier).cleanUp();
    super.dispose();
  }

  Future<void> _initializePlayer() async {
    final MediaPlayerState mediaPlayerState =
        ref.read(mediaPlayerControllerProvider(widget.mediaId));

    if (mediaPlayerState.mediaItem == null) {
      ref
          .read(videoPlayerNotifierProvider.notifier)
          .setError('Media item not found');
      return;
    }

    final String? videoUrl = mediaPlayerState.mediaItem!.videoUrl;
    ref.read(videoPlayerNotifierProvider.notifier).setVideoUrl(videoUrl);

    // Initialize WebView controller if needed
    final VideoPlayerState videoPlayerState =
        ref.read(videoPlayerNotifierProvider);
    if (videoPlayerState.videoType == VideoPlayerType.facebook ||
        videoPlayerState.videoType == VideoPlayerType.tiktok) {
      ref
          .read(videoPlayerNotifierProvider.notifier)
          .initializeWebViewController();
    }
  }

  @override
  Widget build(BuildContext context) {
    final VideoPlayerState videoPlayerState =
        ref.watch(videoPlayerNotifierProvider);

    if (videoPlayerState.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (videoPlayerState.errorMessage != null) {
      return Center(
        child: Text(videoPlayerState.errorMessage!),
      );
    }

    switch (videoPlayerState.videoType) {
      case VideoPlayerType.direct:
        return _buildDirectVideoPlayer();
      case VideoPlayerType.youtube:
        return _buildYoutubePlayer();
      case VideoPlayerType.facebook:
      case VideoPlayerType.tiktok:
        return _buildWebViewPlayer();
      case VideoPlayerType.unknown:
        return Center(
          child: Text('Unsupported video type: ${videoPlayerState.videoUrl}'),
        );
    }
  }

  Widget _buildDirectVideoPlayer() {
    final ChewieController? chewieController = ref
        .watch(mediaPlayerControllerProvider(widget.mediaId).notifier)
        .chewieController;

    if (chewieController == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Initializing video player...'),
          ],
        ),
      );
    }

    return Container(
      color: Colors.black,
      child: Column(
        children: <Widget>[
          Expanded(
            child: Center(
              child: AspectRatio(
                aspectRatio:
                    chewieController.videoPlayerController.value.aspectRatio,
                child: Chewie(
                  controller: chewieController,
                ),
              ),
            ),
          ),
          // Enhanced video controls
          Container(
            padding:
                const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
            color: Colors.black.withValues(alpha: 0.8),
            child: Column(
              children: <Widget>[
                // Video progress indicator
                ValueListenableBuilder<VideoPlayerValue>(
                  valueListenable: chewieController.videoPlayerController,
                  builder: (BuildContext context, VideoPlayerValue value,
                      Widget? child) {
                    final Duration position = value.position;
                    final Duration duration = value.duration;

                    // Format the duration and position
                    final String positionStr =
                        '${position.inMinutes}:${(position.inSeconds % 60).toString().padLeft(2, '0')}';
                    final String durationStr =
                        '${duration.inMinutes}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}';

                    return Column(
                      children: <Widget>[
                        // Custom progress bar
                        LinearProgressIndicator(
                          value: duration.inSeconds > 0
                              ? position.inSeconds / duration.inSeconds
                              : 0.0,
                          backgroundColor: Colors.grey[800],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Theme.of(context).colorScheme.primary,
                          ),
                          minHeight: 5,
                        ),
                        const SizedBox(height: 8),
                        // Time indicators
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Text(
                              positionStr,
                              style: const TextStyle(color: Colors.white70),
                            ),
                            Text(
                              durationStr,
                              style: const TextStyle(color: Colors.white70),
                            ),
                          ],
                        ),
                      ],
                    );
                  },
                ),
                const SizedBox(height: 8),
                // Control buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: <Widget>[
                    // Rewind 10 seconds
                    IconButton(
                      icon: const Icon(Icons.replay_10, color: Colors.white),
                      onPressed: () {
                        final Duration currentPosition = chewieController
                            .videoPlayerController.value.position;
                        final Duration newPosition =
                            currentPosition - const Duration(seconds: 10);
                        chewieController.videoPlayerController.seekTo(
                            newPosition < Duration.zero
                                ? Duration.zero
                                : newPosition);
                      },
                      tooltip: 'إرجاع 10 ثوان',
                    ),
                    // Fullscreen
                    IconButton(
                      icon: const Icon(Icons.fullscreen, color: Colors.white),
                      onPressed: () {
                        chewieController.enterFullScreen();
                      },
                      tooltip: 'ملء الشاشة',
                    ),
                    // Playback speed
                    IconButton(
                      icon: const Icon(Icons.speed, color: Colors.white),
                      onPressed: () {
                        _showPlaybackSpeedDialog(context, chewieController);
                      },
                      tooltip: 'Playback Speed',
                    ),
                    // Forward 10 seconds
                    IconButton(
                      icon: const Icon(Icons.forward_10, color: Colors.white),
                      onPressed: () {
                        final Duration currentPosition = chewieController
                            .videoPlayerController.value.position;
                        final Duration duration = chewieController
                            .videoPlayerController.value.duration;
                        final Duration newPosition =
                            currentPosition + const Duration(seconds: 10);
                        chewieController.videoPlayerController.seekTo(
                            newPosition > duration ? duration : newPosition);
                      },
                      tooltip: 'Forward 10s',
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildYoutubePlayer() {
    final VideoPlayerState videoPlayerState =
        ref.watch(videoPlayerNotifierProvider);

    if (videoPlayerState.youtubeController == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Initializing YouTube player...'),
          ],
        ),
      );
    }

    final YoutubePlayerController youtubeController =
        videoPlayerState.youtubeController!;
    final String? youtubeId = videoPlayerState.youtubeId;

    return Container(
      color: Colors.black,
      child: Column(
        children: <Widget>[
          Expanded(
            child: YoutubePlayer(
              controller: youtubeController,
              showVideoProgressIndicator: true,
              progressIndicatorColor: Colors.red,
              progressColors: const ProgressBarColors(
                playedColor: Colors.red,
                handleColor: Colors.redAccent,
              ),
              onReady: () {
                debugPrint('YouTube Player Ready');
              },
              bottomActions: const <Widget>[
                // Add custom bottom actions to the YouTube player
                CurrentPosition(),
                ProgressBar(
                  isExpanded: true,
                  colors: ProgressBarColors(
                    playedColor: Colors.red,
                    handleColor: Colors.redAccent,
                    bufferedColor: Colors.red,
                    backgroundColor: Colors.grey,
                  ),
                ),
                RemainingDuration(),
                PlaybackSpeedButton(),
              ],
            ),
          ),
          // Enhanced custom controls for YouTube
          Container(
            padding:
                const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
            color: Colors.black.withValues(alpha: 0.8),
            child: Column(
              children: <Widget>[
                // YouTube video info
                if (youtubeId != null)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 12.0),
                    child: Row(
                      children: <Widget>[
                        const Icon(Icons.youtube_searched_for,
                            color: Colors.red),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'YouTube ID: $youtubeId',
                            style: const TextStyle(color: Colors.white70),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                // Control buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: <Widget>[
                    // Rewind 10 seconds
                    IconButton(
                      icon: const Icon(Icons.replay_10, color: Colors.white),
                      onPressed: () {
                        final Duration currentPosition =
                            youtubeController.value.position;
                        final Duration newPosition =
                            currentPosition - const Duration(seconds: 10);
                        youtubeController.seekTo(newPosition < Duration.zero
                            ? Duration.zero
                            : newPosition);
                      },
                      tooltip: 'إرجاع 10 ثوان',
                    ),
                    // Restart
                    IconButton(
                      icon:
                          const Icon(Icons.skip_previous, color: Colors.white),
                      onPressed: () {
                        youtubeController.seekTo(Duration.zero);
                      },
                      tooltip: 'Restart',
                    ),
                    // Play/Pause
                    IconButton(
                      icon: Icon(
                        youtubeController.value.isPlaying
                            ? Icons.pause
                            : Icons.play_arrow,
                        color: Colors.white,
                        size: 32,
                      ),
                      onPressed: () {
                        if (youtubeController.value.isPlaying) {
                          youtubeController.pause();
                        } else {
                          youtubeController.play();
                        }
                      },
                      tooltip:
                          youtubeController.value.isPlaying ? 'Pause' : 'Play',
                    ),
                    // Fullscreen
                    IconButton(
                      icon: Icon(
                        youtubeController.value.isFullScreen
                            ? Icons.fullscreen_exit
                            : Icons.fullscreen,
                        color: Colors.white,
                      ),
                      onPressed: () {
                        youtubeController.toggleFullScreenMode();
                      },
                      tooltip: 'ملء الشاشة',
                    ),
                    // Forward 10 seconds
                    IconButton(
                      icon: const Icon(Icons.forward_10, color: Colors.white),
                      onPressed: () {
                        final Duration currentPosition =
                            youtubeController.value.position;
                        final Duration duration =
                            youtubeController.metadata.duration;
                        final Duration newPosition =
                            currentPosition + const Duration(seconds: 10);
                        youtubeController.seekTo(
                            newPosition > duration ? duration : newPosition);
                      },
                      tooltip: 'Forward 10s',
                    ),
                  ],
                ),
                // Additional options
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    // Open in YouTube app
                    TextButton.icon(
                      icon: const Icon(Icons.open_in_new, color: Colors.red),
                      label: const Text(
                        'Open in YouTube',
                        style: TextStyle(color: Colors.white),
                      ),
                      onPressed: () {
                        if (youtubeId != null) {
                          final MediaPlayerController controller = ref.read(
                              mediaPlayerControllerProvider(widget.mediaId)
                                  .notifier);
                          controller.openInBrowser(
                              'https://www.youtube.com/watch?v=$youtubeId');
                        }
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWebViewPlayer() {
    final VideoPlayerState videoPlayerState =
        ref.watch(videoPlayerNotifierProvider);

    // For Facebook, TikTok, and other platforms, use WebView
    if (videoPlayerState.videoUrl == null) {
      return const Center(
        child: Text('No video URL available'),
      );
    }

    // Initialize the WebView controller if not already initialized
    if (videoPlayerState.webViewController == null) {
      ref
          .read(videoPlayerNotifierProvider.notifier)
          .initializeWebViewController();
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Container(
      color: Colors.black,
      child: Column(
        children: <Widget>[
          // Platform indicator
          Container(
            padding:
                const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
            color: _getPlatformColor(videoPlayerState.videoType)
                .withValues(alpha: 0.8),
            child: Row(
              children: <Widget>[
                Icon(_getPlatformIcon(videoPlayerState.videoType),
                    color: Colors.white),
                const SizedBox(width: 8),
                Text(
                  _getPlatformName(videoPlayerState.videoType),
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          // WebView with loading indicator
          Expanded(
            child: Stack(
              children: <Widget>[
                WebViewWidget(
                  controller: videoPlayerState.webViewController!,
                ),
                if (videoPlayerState.isLoading)
                  const Center(
                    child: CircularProgressIndicator(),
                  ),
              ],
            ),
          ),
          // Enhanced controls for WebView
          Container(
            padding:
                const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
            color: Colors.black.withValues(alpha: 0.8),
            child: Column(
              children: <Widget>[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: <Widget>[
                    // Back button
                    IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () {
                        videoPlayerState.webViewController?.goBack();
                      },
                      tooltip: 'Back',
                    ),
                    // Reload button
                    IconButton(
                      icon: const Icon(Icons.refresh, color: Colors.white),
                      onPressed: () {
                        videoPlayerState.webViewController?.reload();
                      },
                      tooltip: 'Reload',
                    ),
                    // Open in browser button
                    IconButton(
                      icon: const Icon(Icons.open_in_browser,
                          color: Colors.white),
                      onPressed: () {
                        // Open in external browser
                        final MediaPlayerController controller = ref.read(
                            mediaPlayerControllerProvider(widget.mediaId)
                                .notifier);
                        controller.openInBrowser(videoPlayerState.videoUrl!);
                      },
                      tooltip: 'Open in Browser',
                    ),
                    // Forward button
                    IconButton(
                      icon:
                          const Icon(Icons.arrow_forward, color: Colors.white),
                      onPressed: () {
                        videoPlayerState.webViewController?.goForward();
                      },
                      tooltip: 'Forward',
                    ),
                  ],
                ),
                // URL display
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    videoPlayerState.videoUrl!,
                    style: const TextStyle(color: Colors.white70, fontSize: 12),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods for WebView player
  Color _getPlatformColor(VideoPlayerType videoType) {
    switch (videoType) {
      case VideoPlayerType.facebook:
        return const Color(0xFF1877F2); // Facebook blue
      case VideoPlayerType.tiktok:
        return const Color(0xFF000000); // TikTok black
      default:
        return Colors.grey;
    }
  }

  IconData _getPlatformIcon(VideoPlayerType videoType) {
    switch (videoType) {
      case VideoPlayerType.facebook:
        return Icons.facebook;
      case VideoPlayerType.tiktok:
        return Icons.music_note;
      default:
        return Icons.public;
    }
  }

  String _getPlatformName(VideoPlayerType videoType) {
    switch (videoType) {
      case VideoPlayerType.facebook:
        return 'Facebook Video';
      case VideoPlayerType.tiktok:
        return 'TikTok Video';
      default:
        return 'Web Video';
    }
  }

  void _showPlaybackSpeedDialog(
      BuildContext context, ChewieController controller) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Playback Speed'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              _buildSpeedButton(context, 0.5, controller),
              _buildSpeedButton(context, 0.75, controller),
              _buildSpeedButton(context, 1.0, controller),
              _buildSpeedButton(context, 1.25, controller),
              _buildSpeedButton(context, 1.5, controller),
              _buildSpeedButton(context, 2.0, controller),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSpeedButton(
      BuildContext context, double speed, ChewieController controller) {
    return ListTile(
      title: Text('${speed}x'),
      onTap: () {
        controller.videoPlayerController.setPlaybackSpeed(speed);
        Navigator.of(context).pop();
      },
    );
  }

  void _showYoutubePlaybackSpeedDialog(BuildContext context) {
    final VideoPlayerState videoPlayerState =
        ref.read(videoPlayerNotifierProvider);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Playback Speed'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              _buildYoutubeSpeedButton(
                  context, 0.5, videoPlayerState.youtubeController),
              _buildYoutubeSpeedButton(
                  context, 0.75, videoPlayerState.youtubeController),
              _buildYoutubeSpeedButton(
                  context, 1.0, videoPlayerState.youtubeController),
              _buildYoutubeSpeedButton(
                  context, 1.25, videoPlayerState.youtubeController),
              _buildYoutubeSpeedButton(
                  context, 1.5, videoPlayerState.youtubeController),
              _buildYoutubeSpeedButton(
                  context, 2.0, videoPlayerState.youtubeController),
            ],
          ),
        );
      },
    );
  }

  Widget _buildYoutubeSpeedButton(
      BuildContext context, double speed, YoutubePlayerController? controller) {
    return ListTile(
      title: Text('${speed}x'),
      onTap: () {
        // YouTube API doesn't directly support setting playback speed
        // This is a workaround using JavaScript
        controller?.reload();
        Navigator.of(context).pop();
      },
    );
  }
}
