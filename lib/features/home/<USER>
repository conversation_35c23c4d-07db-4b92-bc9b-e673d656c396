import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../data/enums/media_type_enum.dart';
import '../../data/models/media_items/media_navigation_helper.dart';
import '../../data/models/media_items/media_player_controller.dart';
import '../../data/models/media_items/media_provider.dart';
import '../../data/models/media_player_state.dart';
import '../../data/models/media_ui_model.dart';
import '../../data/models/tab_categories/tab_category_model.dart';
import '../../data/models/tab_categories/tab_category_provider.dart';
import '../../routing/app_router.dart';
import '../../utils/icons_changer.dart';
import 'widgets/category_items_page.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen>
    with TickerProviderStateMixin {
  late TabController _mainTabController;

  @override
  void initState() {
    super.initState();
    // Initialize with 5 tabs, will update when data is loaded
    _mainTabController = TabController(
      length: 5,
      vsync: this,
    );

    _mainTabController.addListener(_handleTabChange);
  }

  void _handleTabChange() {
    if (!_mainTabController.indexIsChanging) {
      ref
          .read(selectedMainTabProvider.notifier)
          .setTab(_mainTabController.index);
      // Reset secondary tab when main tab changes
      ref.read(selectedSecondaryTabProvider.notifier).setTab(0);
    }
  }

  @override
  void dispose() {
    _mainTabController.removeListener(_handleTabChange);
    _mainTabController.dispose();
    super.dispose();
  }

  Future<bool> _showExitDialog(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          builder: (BuildContext dialogContext) {
            return AlertDialog(
              title: const Text('Exit App'),
              content: const Text('Are you sure you want to exit the app?'),
              actions: <Widget>[
                TextButton(
                  onPressed: () => dialogContext.pop(false),
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    dialogContext.pop(true);
                  },
                  child: const Text('Exit'),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  @override
  Widget build(BuildContext context) {
    // Watch the provider states
    final int selectedMainTab = ref.watch(selectedMainTabProvider);
    final int selectedSecondaryTab = ref.watch(selectedSecondaryTabProvider);
    final String? miniPlayerMediaId = ref.watch(miniPlayerMediaIdProvider);

    // Get tab categories from the provider
    final AsyncValue<List<MainTabCategory>> mainCategoriesAsync =
        ref.watch(mainTabCategoriesProvider);

    // Sync the tab controller with the provider state
    if (_mainTabController.index != selectedMainTab) {
      _mainTabController.animateTo(selectedMainTab);
    }

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? result) async {
        if (didPop) {
          return;
        }

        // Store the context in a local variable to avoid using it across async gaps
        final BuildContext currentContext = context;
        final bool shouldPop = await _showExitDialog(currentContext);

        if (shouldPop && mounted) {
          if (mounted && currentContext.mounted && currentContext.canPop()) {
            // Use go_router for navigation within the app
            currentContext.pop();
          } else if (mounted) {
            // We're at the root route, use SystemNavigator to exit the app
            // This is the proper way to exit an app in Flutter 3.29.3
            SystemNavigator.pop();
          }
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('الدكتور الفريح'),
          // Use go_router for navigation actions in the AppBar too
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () {
                debugPrint('Navigating to search: ${SGRoute.search.route}');
                context.push(SGRoute.search.route);
              },
            ),
            IconButton(
              icon: const Icon(Icons.settings),
              onPressed: () {
                debugPrint('Navigating to settings: ${SGRoute.settings.route}');
                context.push(SGRoute.settings.route);
              },
            ),
          ],
          bottom: mainCategoriesAsync.when(
            data: (List<MainTabCategory> categories) {
              // Update tab controller if needed
              if (_mainTabController.length != categories.length) {
                // Save the current index before disposing
                final int currentIndex = _mainTabController.index;

                // Dispose the old controller
                _mainTabController.removeListener(() {});
                _mainTabController.dispose();

                // Create a new controller with the correct length
                _mainTabController = TabController(
                  length: categories.length,
                  vsync: this,
                  initialIndex:
                      currentIndex < categories.length ? currentIndex : 0,
                );

                // Add the listener to the new controller
                _mainTabController.addListener(_handleTabChange);
              }

              // Ensure the controller index matches the selected tab
              if (_mainTabController.index != selectedMainTab &&
                  selectedMainTab < categories.length) {
                _mainTabController.animateTo(selectedMainTab);
              }

              return TabBar(
                controller: _mainTabController,
                isScrollable: true,
                tabs: categories.map((MainTabCategory category) {
                  return Tab(
                    text: category.titleAr,
                    icon: Icon(category.icon),
                  );
                }).toList(),
              );
            },
            loading: () => const PreferredSize(
              preferredSize: Size.fromHeight(48),
              child: Center(child: CircularProgressIndicator()),
            ),
            error: (Object error, StackTrace stackTrace) => PreferredSize(
              preferredSize: const Size.fromHeight(48),
              child: Center(child: Text('Error: $error')),
            ),
          ),
        ),
        body: mainCategoriesAsync.when(
          data: (List<MainTabCategory> categories) {
            return Column(
              children: <Widget>[
                // Secondary tabs
                _buildSecondaryTabs(
                    categories, selectedMainTab, selectedSecondaryTab),

                // Content area
                Expanded(
                  child: _buildContentArea(
                      categories, selectedMainTab, selectedSecondaryTab),
                ),

                // Mini player (if media is selected)
                if (miniPlayerMediaId != null)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 50),
                    child: _buildMiniPlayer(miniPlayerMediaId),
                  ),
              ],
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (Object error, StackTrace stackTrace) => Center(
            child: Text('Error loading categories: $error'),
          ),
        ),
      ),
    );
  }

  Widget _buildSecondaryTabs(List<MainTabCategory> categories, int mainTabIndex,
      int secondaryTabIndex) {
    if (mainTabIndex >= categories.length) {
      return const SizedBox.shrink();
    }

    final List<SecondaryTabCategory> secondaryTabs =
        categories[mainTabIndex].secondaryTabs;

    return Container(
      height: 50,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: Colors.black.withAlpha(26), // Equivalent to opacity 0.1
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: secondaryTabs.length,
        itemBuilder: (BuildContext context, int index) {
          final bool isSelected = index == secondaryTabIndex;
          final SecondaryTabCategory tab = secondaryTabs[index];

          return InkWell(
            onTap: () {
              ref.read(selectedSecondaryTabProvider.notifier).setTab(index);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Colors.transparent,
                    width: 3,
                  ),
                ),
              ),
              child: Text(
                tab.titleAr,
                style: TextStyle(
                  color: isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildContentArea(List<MainTabCategory> categories, int mainTabIndex,
      int secondaryTabIndex) {
    if (mainTabIndex >= categories.length ||
        secondaryTabIndex >= categories[mainTabIndex].secondaryTabs.length) {
      return const Center(
        child: Text('Invalid tab selection'),
      );
    }

    final MainTabCategory mainCategory = categories[mainTabIndex];
    final SecondaryTabCategory secondaryCategory =
        mainCategory.secondaryTabs[secondaryTabIndex];

    // Special case for tweets
    final MediaType mediaType =
        MediaType.fromString(secondaryCategory.mediaType);
    if (mediaType == MediaType.tweet) {
      return _buildTweetsContent();
    }

    return _buildMediaContent(
        secondaryCategory.mediaType, secondaryCategory.id);
  }

  Widget _buildMediaContent(String mediaType, String category) {
    // Get filtered media items using the provider
    final List<MediaUiModel> items = ref.watch(categoryFilteredMediaProvider(
        mediaType: mediaType, category: category));

    if (items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Icon(
              Icons.search_off,
              size: 64,
              color: Theme.of(context).colorScheme.secondary,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد عناصر متاحة في $category',
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () => ref.refresh(mediaListProvider),
              child: const Text('تحديث'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: items.length,
      padding: const EdgeInsets.all(8.0),
      itemBuilder: (BuildContext context, int index) {
        final MediaUiModel item = items[index];

        return ItemsCard(
          item: item,
          ref: ref,
          mediaType: mediaType,
        );
      },
    );
  }

  Widget _buildTweetsContent() {
    // Get filtered media items for tweets
    final List<MediaUiModel> tweetItems =
        ref.watch(filteredMediaProvider).where((MediaUiModel item) {
      final MediaType mediaType = MediaType.fromString(item.type);
      return mediaType == MediaType.tweet ||
          (item.tweetContent != null && item.tweetContent!.isNotEmpty);
    }).toList();

    if (tweetItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Icon(
              Icons.format_quote,
              size: 64,
              color: Theme.of(context).colorScheme.secondary,
            ),
            const SizedBox(height: 16),
            const Text(
              'لا توجد تغريدات متاحة',
              style: TextStyle(fontSize: 18),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                context.push(SGRoute.tweets.route);
              },
              child: const Text('عرض جميع التغريدات'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: tweetItems.length,
      padding: const EdgeInsets.all(12.0),
      itemBuilder: (BuildContext context, int index) {
        final MediaUiModel tweet = tweetItems[index];

        return Card(
          elevation: 2,
          margin: const EdgeInsets.only(bottom: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                // Tweet header with author info
                Row(
                  children: <Widget>[
                    CircleAvatar(
                      radius: 20,
                      backgroundImage: tweet.thumbnailUrl != null
                          ? NetworkImage(tweet.thumbnailUrl!)
                          : null,
                      child: tweet.thumbnailUrl == null
                          ? const Icon(Icons.person)
                          : null,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Text(
                            tweet.tweetAuthor ?? 'د. الفريح',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            '@${tweet.tweetAuthor?.replaceAll(' ', '_').toLowerCase() ?? 'dralfarih'}',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.verified,
                      color: Colors.blue[400],
                      size: 18,
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // Tweet content
                Text(
                  tweet.tweetContent ??
                      tweet.metadata?.description ??
                      tweet.title,
                  style: const TextStyle(fontSize: 16),
                  textDirection: TextDirection.rtl,
                ),

                // Tweet image if available
                if (tweet.thumbnailUrl != null && tweet.type != 'audio')
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 12.0),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Image.network(
                        tweet.thumbnailUrl!,
                        fit: BoxFit.cover,
                        errorBuilder: (BuildContext context, Object error,
                            StackTrace? stackTrace) {
                          return const SizedBox.shrink();
                        },
                      ),
                    ),
                  ),

                // Audio player for audio tweets
                if (tweet.audioUrl != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 12.0),
                    child: ElevatedButton.icon(
                      icon: const Icon(Icons.play_arrow),
                      label: const Text('Play Audio'),
                      onPressed: () {
                        // Set the mini player with this tweet's audio
                        ref
                            .read(miniPlayerMediaIdProvider.notifier)
                            .setMediaId(tweet.id);
                        // Also set the selected media

                        ref
                            .read(selectedMediaNotifierProvider.notifier)
                            .selectedMedia = tweet.id;
                      },
                    ),
                  ),

                const SizedBox(height: 12),

                // Tweet actions
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: <Widget>[
                    _buildTweetAction(Icons.favorite_border,
                        tweet.metadata?.likeCount?.toString() ?? '0'),
                    _buildTweetAction(Icons.repeat,
                        tweet.metadata?.retweetCount?.toString() ?? '0'),
                    _buildTweetAction(Icons.chat_bubble_outline,
                        tweet.metadata?.commentCount?.toString() ?? '0'),
                    _buildTweetAction(Icons.share_outlined, ''),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTweetAction(IconData icon, String count) {
    return Row(
      children: <Widget>[
        Icon(
          icon,
          size: 18,
          color: Colors.grey[600],
        ),
        if (count.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(left: 4.0),
            child: Text(
              count,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildMiniPlayer(String mediaId) {
    // Create a provider that we can safely watch
    final MediaPlayerControllerProvider mediaPlayerProvider =
        mediaPlayerControllerProvider(mediaId);

    // Get the media player state and controller
    final MediaPlayerState? mediaPlayerState = ref.watch(mediaPlayerProvider);
    final MediaPlayerController? mediaPlayerController =
        ref.watch(mediaPlayerProvider.notifier);

    // Get the media item details
    final MediaUiModel? mediaItem = ref
        .watch(filteredMediaProvider)
        .where((MediaUiModel item) => item.id == mediaId)
        .firstOrNull;

    if (mediaPlayerState == null ||
        mediaPlayerController == null ||
        mediaItem == null) {
      return const SizedBox.shrink();
    }

    // Navigation is now handled by MediaNavigationHelper

    // Determine the media type icon
    IconData mediaTypeIcon;
    String mediaTypeText = '';

    if (mediaItem.type.toLowerCase() == 'audio') {
      mediaTypeIcon = Icons.audiotrack;
    } else if (mediaItem.type.toLowerCase() == 'video') {
      mediaTypeIcon = Icons.videocam;
    } else if (mediaItem.type.toLowerCase() == 'pdf') {
      mediaTypeIcon = Icons.picture_as_pdf;
      mediaTypeText = 'ملف PDF';
    } else if (mediaItem.type.toLowerCase() == 'tweet') {
      mediaTypeIcon = Icons.format_quote;
      mediaTypeText = 'تغريدة';
    } else if (mediaItem.type.toLowerCase() == 'document') {
      mediaTypeIcon = Icons.insert_drive_file;
      mediaTypeText = 'مستند';
    } else {
      mediaTypeIcon = Icons.insert_drive_file;
      mediaTypeText = mediaItem.category;
    }

    return GestureDetector(
      onTap: () {
        // Navigate to the appropriate full player when the mini player is tapped
        const MediaNavigationHelper().navigateToMediaPlayer(context, mediaItem);
      },
      child: Container(
        height: 70,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          boxShadow: <BoxShadow>[
            BoxShadow(
              color: Colors.black.withAlpha(26),
              blurRadius: 4,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: Row(
          children: <Widget>[
            // Media type icon
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                mediaTypeIcon,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(width: 12),

            // Media info and progress
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    mediaItem.title,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                  const SizedBox(height: 4),
                  if (mediaItem.type.toLowerCase() == 'audio' ||
                      mediaItem.type.toLowerCase() == 'video')
                    LinearProgressIndicator(
                      value: mediaPlayerState.currentPosition.inMilliseconds >
                                  0 &&
                              mediaPlayerState.totalDuration.inMilliseconds > 0
                          ? mediaPlayerState.currentPosition.inMilliseconds /
                              mediaPlayerState.totalDuration.inMilliseconds
                          : 0.0,
                    ),
                  if (mediaItem.type.toLowerCase() == 'audio' ||
                      mediaItem.type.toLowerCase() == 'video')
                    Text(
                      '${_formatDuration(mediaPlayerState.currentPosition)} / ${_formatDuration(mediaPlayerState.totalDuration)}',
                      style: const TextStyle(fontSize: 12),
                    ),
                  if (mediaItem.type.toLowerCase() != 'audio' &&
                      mediaItem.type.toLowerCase() != 'video')
                    Text(
                      mediaTypeText,
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                ],
              ),
            ),

            // Play/Pause button (only for audio/video)
            if (mediaItem.type.toLowerCase() == 'audio' ||
                mediaItem.type.toLowerCase() == 'video')
              IconButton(
                icon: Icon(
                  mediaPlayerState.isPlaying ? Icons.pause : Icons.play_arrow,
                  color: Theme.of(context).colorScheme.primary,
                ),
                onPressed: () {
                  if (mediaPlayerState.isPlaying) {
                    mediaPlayerController.pause();
                  } else {
                    mediaPlayerController.play();
                  }
                },
              ),

            // Expand button to show full player
            IconButton(
              icon: const Icon(Icons.fullscreen),
              onPressed: () {
                // Navigate to the appropriate full player
                const MediaNavigationHelper()
                    .navigateToMediaPlayer(context, mediaItem);
              },
            ),

            // Close button
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: () {
                // Pause playback if it's playing
                if (mediaPlayerState.isPlaying) {
                  mediaPlayerController.pause();
                }
                // Close mini player
                ref.read(miniPlayerMediaIdProvider.notifier).clearMediaId();
                // Also clear the selected media
                ref
                    .read(selectedMediaNotifierProvider.notifier)
                    .clearSelection();
              },
            ),
          ],
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final String minutes = twoDigits(duration.inMinutes.remainder(60));
    final String seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}

class ItemsCard extends StatelessWidget {
  const ItemsCard({
    super.key,
    required this.item,
    required this.ref,
    required this.mediaType,
  });

  final MediaUiModel item;
  final WidgetRef ref;
  final String mediaType;

  @override
  Widget build(BuildContext context) {
    final MediaType type = MediaType.fromString(item.type);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 6.0),
      clipBehavior: type == MediaType.video ? Clip.antiAlias : Clip.none,
      child: InkWell(
        onTap: () => _handleItemTap(context),
        child: type == MediaType.video
            ? _buildVideoContent(context)
            : type == MediaType.audio
                ? _buildAudioContent(context)
                : type.isDocument || type == MediaType.text
                    ? _buildDocumentContent(context)
                    : _buildDefaultContent(context),
      ),
    );
  }

  Widget _buildVideoContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        // Thumbnail with play button
        Stack(
          alignment: Alignment.center,
          children: <Widget>[
            AspectRatio(
              aspectRatio: 16 / 9,
              child: item.thumbnailUrl != null
                  ? Image.network(item.thumbnailUrl!,
                      fit: BoxFit.cover,
                      errorBuilder: (_, __, ___) =>
                          _placeholderIcon(Icons.videocam))
                  : _placeholderIcon(Icons.videocam),
            ),
            const CircleAvatar(
              backgroundColor: Colors.black54,
              child: Icon(Icons.play_arrow, color: Colors.white),
            ),
          ],
        ),
        // Details
        Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(item.title,
                  style: Theme.of(context).textTheme.titleMedium,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis),
              if (item.metadata?.description != null) ...<Widget>[
                const SizedBox(height: 4),
                Text(item.metadata!.description!,
                    style: Theme.of(context).textTheme.bodySmall,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis),
              ],
              const SizedBox(height: 8),
              _buildInfoRow(context),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAudioContent(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Row(
        children: <Widget>[
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(Icons.headphones,
                color: Theme.of(context).colorScheme.primary, size: 32),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(item.title,
                    style: Theme.of(context).textTheme.titleMedium,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis),
                const SizedBox(height: 4),
                Text(item.category,
                    style: Theme.of(context).textTheme.bodySmall,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis),
                const SizedBox(height: 4),
                Row(
                  children: <Widget>[
                    Icon(Icons.access_time, size: 14, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(_formatDuration(),
                        style:
                            TextStyle(color: Colors.grey[600], fontSize: 12)),
                  ],
                ),
              ],
            ),
          ),
          Icon(Icons.play_circle_outline,
              color: Theme.of(context).colorScheme.primary),
        ],
      ),
    );
  }

  Widget _buildDocumentContent(BuildContext context) {
    return Column(
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              // Book/PDF icon
              Container(
                width: 60,
                height: 80,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(4),
                  boxShadow: const <BoxShadow>[
                    BoxShadow(
                        color: Colors.black12,
                        blurRadius: 4,
                        offset: Offset(0, 2))
                  ],
                ),
                child: item.thumbnailUrl != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(4),
                        child: Image.network(item.thumbnailUrl!,
                            fit: BoxFit.cover,
                            errorBuilder: (_, __, ___) =>
                                const Icon(Icons.picture_as_pdf, size: 40)),
                      )
                    : Icon(Icons.picture_as_pdf,
                        size: 40, color: Theme.of(context).colorScheme.primary),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(item.title,
                        style: Theme.of(context).textTheme.titleMedium,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis),
                    const SizedBox(height: 8),
                    _buildInfoRow(context, showPages: true),
                  ],
                ),
              ),
            ],
          ),
        ),
        if (item.metadata?.description != null)
          Padding(
            padding: const EdgeInsets.fromLTRB(12.0, 0, 12.0, 12.0),
            child: Text(item.metadata!.description!,
                style: Theme.of(context).textTheme.bodySmall,
                maxLines: 2,
                overflow: TextOverflow.ellipsis),
          ),
      ],
    );
  }

  Widget _buildDefaultContent(BuildContext context) {
    return ListTile(
      title: Text(item.title),
      subtitle: Text(item.category),
      leading: item.thumbnailUrl != null
          ? Image.network(item.thumbnailUrl!,
              width: 50,
              height: 50,
              fit: BoxFit.cover,
              errorBuilder: (_, __, ___) =>
                  Icon(getIconForType(mediaType), size: 50))
          : Icon(getIconForType(mediaType), size: 50),
    );
  }

  Widget _buildInfoRow(BuildContext context, {bool showPages = false}) {
    return Row(
      children: <Widget>[
        if (showPages) ...<Widget>[
          Icon(Icons.insert_drive_file, size: 14, color: Colors.grey[600]),
          const SizedBox(width: 4),
          Text(
            item.metadata?.additionalInfo?['pages'] != null
                ? '${item.metadata!.additionalInfo!['pages']} pages'
                : 'Document',
            style: TextStyle(color: Colors.grey[600], fontSize: 12),
          ),
          const SizedBox(width: 16),
        ] else ...<Widget>[
          Icon(Icons.access_time, size: 14, color: Colors.grey[600]),
          const SizedBox(width: 4),
          Text(_formatDuration(),
              style: TextStyle(color: Colors.grey[600], fontSize: 12)),
          const SizedBox(width: 16),
        ],
        Icon(Icons.calendar_today, size: 14, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Text(_formatDate(),
            style: TextStyle(color: Colors.grey[600], fontSize: 12)),
      ],
    );
  }

  Widget _placeholderIcon(IconData icon) {
    return Container(
      color: Colors.grey[300],
      child: Center(child: Icon(icon, size: 50)),
    );
  }

  void _handleItemTap(BuildContext context) {
    final MediaType mediaType = MediaType.fromString(item.type);
    if (mediaType.isDocument ||
        mediaType == MediaType.text ||
        (item.documentUrl != null && item.documentUrl!.isNotEmpty)) {
      const MediaNavigationHelper().navigateToMediaPlayer(context, item);
    } else if (mediaType == MediaType.video) {
      context.push('${SGRoute.mediaPlayer.route}/${item.id}');
    } else {
      ref.read(miniPlayerMediaIdProvider.notifier).setMediaId(item.id);
      ref.read(selectedMediaNotifierProvider.notifier).selectedMedia = item.id;
    }
  }

  String _formatDuration() {
    if (item.durationSeconds == null) return 'Unknown';
    final Duration duration = Duration(seconds: item.durationSeconds!);
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final String minutes = twoDigits(duration.inMinutes.remainder(60));
    final String seconds = twoDigits(duration.inSeconds.remainder(60));
    return duration.inHours > 0
        ? '${duration.inHours}:$minutes:$seconds'
        : '$minutes:$seconds';
  }

  String _formatDate() {
    final DateTime date = item.createdAt ?? DateTime.now();
    return '${date.day}/${date.month}/${date.year}';
  }
}
