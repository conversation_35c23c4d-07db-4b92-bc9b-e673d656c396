// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'media_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

/// 1) AsyncNotifier for loading raw domain items
@ProviderFor(MediaList)
const mediaListProvider = MediaListProvider._();

/// 1) AsyncNotifier for loading raw domain items
final class MediaListProvider
    extends $AsyncNotifierProvider<MediaList, List<MediaItem>> {
  /// 1) AsyncNotifier for loading raw domain items
  const MediaListProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'mediaListProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$mediaListHash();

  @$internal
  @override
  MediaList create() => MediaList();

  @$internal
  @override
  $AsyncNotifierProviderElement<MediaList, List<MediaItem>> $createElement(
          $ProviderPointer pointer) =>
      $AsyncNotifierProviderElement(pointer);
}

String _$mediaListHash() => r'6f99f8e02b61d5e498f2d97c0067e56b2010e96e';

abstract class _$MediaList extends $AsyncNotifier<List<MediaItem>> {
  FutureOr<List<MediaItem>> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<List<MediaItem>>>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<AsyncValue<List<MediaItem>>>,
        AsyncValue<List<MediaItem>>,
        Object?,
        Object?>;
    element.handleValue(ref, created);
  }
}

/// 2) AsyncNotifier for converting domain → UI models with persistence
@ProviderFor(MediaUiList)
const mediaUiListProvider = MediaUiListProvider._();

/// 2) AsyncNotifier for converting domain → UI models with persistence
final class MediaUiListProvider
    extends $AsyncNotifierProvider<MediaUiList, List<MediaUiModel>> {
  /// 2) AsyncNotifier for converting domain → UI models with persistence
  const MediaUiListProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'mediaUiListProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$mediaUiListHash();

  @$internal
  @override
  MediaUiList create() => MediaUiList();

  @$internal
  @override
  $AsyncNotifierProviderElement<MediaUiList, List<MediaUiModel>> $createElement(
          $ProviderPointer pointer) =>
      $AsyncNotifierProviderElement(pointer);
}

String _$mediaUiListHash() => r'ff80a5c2b2e794e7a3f8b707724eef1ad881e832';

abstract class _$MediaUiList extends $AsyncNotifier<List<MediaUiModel>> {
  FutureOr<List<MediaUiModel>> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<List<MediaUiModel>>>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<AsyncValue<List<MediaUiModel>>>,
        AsyncValue<List<MediaUiModel>>,
        Object?,
        Object?>;
    element.handleValue(ref, created);
  }
}

/// 3) Simple String state for search query with persistence
@ProviderFor(MediaSearch)
const mediaSearchProvider = MediaSearchProvider._();

/// 3) Simple String state for search query with persistence
final class MediaSearchProvider extends $NotifierProvider<MediaSearch, String> {
  /// 3) Simple String state for search query with persistence
  const MediaSearchProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'mediaSearchProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$mediaSearchHash();

  @$internal
  @override
  MediaSearch create() => MediaSearch();

  @$internal
  @override
  $NotifierProviderElement<MediaSearch, String> $createElement(
          $ProviderPointer pointer) =>
      $NotifierProviderElement(pointer);

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<String>(value),
    );
  }
}

String _$mediaSearchHash() => r'677683289d855db8c525307de174d050c5916f35';

abstract class _$MediaSearch extends $Notifier<String> {
  String build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<String>;
    final element = ref.element
        as $ClassProviderElement<AnyNotifier<String>, String, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

/// 4) Computed provider that filters the UI list by the search query with persistence
@ProviderFor(filteredMedia)
const filteredMediaProvider = FilteredMediaProvider._();

/// 4) Computed provider that filters the UI list by the search query with persistence
final class FilteredMediaProvider
    extends $FunctionalProvider<List<MediaUiModel>, List<MediaUiModel>>
    with $Provider<List<MediaUiModel>> {
  /// 4) Computed provider that filters the UI list by the search query with persistence
  const FilteredMediaProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'filteredMediaProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$filteredMediaHash();

  @$internal
  @override
  $ProviderElement<List<MediaUiModel>> $createElement(
          $ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  List<MediaUiModel> create(Ref ref) {
    return filteredMedia(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<MediaUiModel> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<List<MediaUiModel>>(value),
    );
  }
}

String _$filteredMediaHash() => r'04c6d8ec723cb345d552cb3dde5eef7f3773f56d';

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
