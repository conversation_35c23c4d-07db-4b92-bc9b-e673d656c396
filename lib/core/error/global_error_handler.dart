import 'dart:async';
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../logging/app_logger.dart';
import 'app_error.dart';

/// Global error handler for the application
class GlobalErrorHandler {
  static final Logger _logger = AppLogger.logger;
  static bool _isInitialized = false;

  /// Initialize global error handling
  static void initialize() {
    if (_isInitialized) return;

    // Handle Flutter framework errors
    FlutterError.onError = (FlutterErrorDetails details) {
      _handleFlutterError(details);
    };

    // Handle platform errors (outside Flutter)
    PlatformDispatcher.instance.onError = (Object error, StackTrace stack) {
      _handlePlatformError(error, stack);
      return true;
    };

    // Handle async errors in zones
    runZonedGuarded(() {
      // Your app initialization code here
    }, (Object error, StackTrace stack) {
      _handleZoneError(error, stack);
    });

    _isInitialized = true;
    _logger.i('Global error handler initialized');
  }

  /// Handle Flutter framework errors
  static void _handleFlutterError(FlutterErrorDetails details) {
    final AppError appError = _convertToAppError(details.exception, details.stack);
    
    _logError(
      'Flutter Error',
      appError,
      details.stack,
      additionalInfo: {
        'library': details.library,
        'context': details.context?.toString(),
        'informationCollector': details.informationCollector?.toString(),
      },
    );

    // In debug mode, show the red screen
    if (kDebugMode) {
      FlutterError.presentError(details);
    }
  }

  /// Handle platform errors
  static void _handlePlatformError(Object error, StackTrace stack) {
    final AppError appError = _convertToAppError(error, stack);
    
    _logError(
      'Platform Error',
      appError,
      stack,
    );
  }

  /// Handle zone errors
  static void _handleZoneError(Object error, StackTrace stack) {
    final AppError appError = _convertToAppError(error, stack);
    
    _logError(
      'Zone Error',
      appError,
      stack,
    );
  }

  /// Convert any error to AppError
  static AppError _convertToAppError(Object error, StackTrace? stack) {
    if (error is AppError) {
      return error;
    }

    // Handle specific error types
    if (error.toString().contains('SocketException') ||
        error.toString().contains('HttpException')) {
      return AppError.network(
        message: error.toString(),
        isRetryable: true,
      );
    }

    if (error.toString().contains('TimeoutException')) {
      return AppError.timeout(
        message: error.toString(),
      );
    }

    if (error.toString().contains('FileSystemException')) {
      return AppError.file(
        message: error.toString(),
      );
    }

    // Default to unknown error
    return AppError.unknown(
      message: error.toString(),
      originalError: error,
      stackTrace: stack,
    );
  }

  /// Log error with appropriate level
  static void _logError(
    String type,
    AppError error,
    StackTrace? stack, {
    Map<String, dynamic>? additionalInfo,
  }) {
    final Map<String, dynamic> errorData = {
      'type': type,
      'error': error.toString(),
      'severity': error.severity.name,
      'isRetryable': error.isRetryable,
      'timestamp': DateTime.now().toIso8601String(),
      if (additionalInfo != null) ...additionalInfo,
    };

    switch (error.severity) {
      case ErrorSeverity.low:
        _logger.d('$type: ${error.userMessage}', error: error, stackTrace: stack);
        break;
      case ErrorSeverity.medium:
        _logger.w('$type: ${error.userMessage}', error: error, stackTrace: stack);
        break;
      case ErrorSeverity.high:
      case ErrorSeverity.critical:
        _logger.e('$type: ${error.userMessage}', error: error, stackTrace: stack);
        break;
    }

    // Send to crash reporting service in production
    if (kReleaseMode && error.severity == ErrorSeverity.critical) {
      _sendToCrashReporting(error, stack, errorData);
    }
  }

  /// Send error to crash reporting service (Firebase Crashlytics, Sentry, etc.)
  static void _sendToCrashReporting(
    AppError error,
    StackTrace? stack,
    Map<String, dynamic> errorData,
  ) {
    // TODO: Implement crash reporting service integration
    // Example for Firebase Crashlytics:
    // FirebaseCrashlytics.instance.recordError(
    //   error,
    //   stack,
    //   information: errorData.entries.map((e) => '${e.key}: ${e.value}').toList(),
    // );
    
    _logger.e('Critical error sent to crash reporting', error: error, stackTrace: stack);
  }

  /// Handle errors in Riverpod providers
  static void handleProviderError(Object error, StackTrace stack, Ref ref) {
    final AppError appError = _convertToAppError(error, stack);
    
    _logError(
      'Provider Error',
      appError,
      stack,
      additionalInfo: {
        'provider': ref.toString(),
      },
    );

    // Optionally invalidate related providers on certain errors
    if (appError is NetworkError && appError.isRetryable) {
      // Could invalidate network-related providers here
    }
  }

  /// Show error dialog to user
  static void showErrorDialog(
    BuildContext context,
    AppError error, {
    VoidCallback? onRetry,
  }) {
    showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('خطأ'),
          content: Text(error.userMessage),
          actions: <Widget>[
            if (error.isRetryable && onRetry != null)
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  onRetry();
                },
                child: const Text('إعادة المحاولة'),
              ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('موافق'),
            ),
          ],
        );
      },
    );
  }

  /// Show error snackbar
  static void showErrorSnackBar(
    BuildContext context,
    AppError error, {
    VoidCallback? onRetry,
  }) {
    final SnackBar snackBar = SnackBar(
      content: Text(error.userMessage),
      backgroundColor: Theme.of(context).colorScheme.error,
      action: error.isRetryable && onRetry != null
          ? SnackBarAction(
              label: 'إعادة المحاولة',
              onPressed: onRetry,
              textColor: Theme.of(context).colorScheme.onError,
            )
          : null,
      duration: Duration(
        seconds: error.severity == ErrorSeverity.high ? 6 : 4,
      ),
    );

    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }
}

/// Riverpod provider for error handling
final globalErrorHandlerProvider = Provider<GlobalErrorHandler>((ref) {
  return GlobalErrorHandler();
});

/// Extension to handle errors in AsyncValue
extension AsyncValueErrorHandling<T> on AsyncValue<T> {
  /// Handle error with user feedback
  void handleError(
    BuildContext context, {
    VoidCallback? onRetry,
    bool showDialog = false,
  }) {
    whenOrNull(
      error: (error, stack) {
        final AppError appError = GlobalErrorHandler._convertToAppError(error, stack);
        
        if (showDialog) {
          GlobalErrorHandler.showErrorDialog(context, appError, onRetry: onRetry);
        } else {
          GlobalErrorHandler.showErrorSnackBar(context, appError, onRetry: onRetry);
        }
      },
    );
  }
}

/// Widget to wrap providers with error handling
class ErrorBoundary extends ConsumerWidget {
  const ErrorBoundary({
    super.key,
    required this.child,
    this.onError,
  });

  final Widget child;
  final void Function(Object error, StackTrace stack)? onError;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return child;
  }
}

/// Mixin for widgets that need error handling
mixin ErrorHandlingMixin<T extends StatefulWidget> on State<T> {
  void handleError(Object error, [StackTrace? stack]) {
    final AppError appError = GlobalErrorHandler._convertToAppError(error, stack);
    GlobalErrorHandler.showErrorSnackBar(context, appError);
  }

  void handleAsyncError(Future<void> future) {
    future.catchError((Object error, StackTrace stack) {
      handleError(error, stack);
    });
  }
}
