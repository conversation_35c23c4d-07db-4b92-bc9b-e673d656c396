import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';

/// Professional logging system for the application
class AppLogger {
  static Logger? _logger;
  static late String _logFilePath;

  /// Get the logger instance
  static Logger get logger {
    _logger ??= _createLogger();
    return _logger!;
  }

  /// Initialize the logging system
  static Future<void> initialize() async {
    if (kReleaseMode) {
      // In release mode, set up file logging
      await _setupFileLogging();
    }
    
    _logger = _createLogger();
    _logger!.i('AppLogger initialized');
  }

  /// Create logger with appropriate configuration
  static Logger _createLogger() {
    return Logger(
      filter: _LogFilter(),
      printer: kDebugMode ? _DevelopmentPrinter() : _ProductionPrinter(),
      output: kReleaseMode ? _FileOutput() : null,
      level: kDebugMode ? Level.debug : Level.info,
    );
  }

  /// Setup file logging for production
  static Future<void> _setupFileLogging() async {
    try {
      final Directory appDocDir = await getApplicationDocumentsDirectory();
      final Directory logDir = Directory('${appDocDir.path}/logs');
      
      if (!await logDir.exists()) {
        await logDir.create(recursive: true);
      }
      
      final String timestamp = DateTime.now().toIso8601String().split('T')[0];
      _logFilePath = '${logDir.path}/app_log_$timestamp.txt';
    } catch (e) {
      debugPrint('Failed to setup file logging: $e');
    }
  }

  /// Log debug message
  static void debug(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? data}) {
    logger.d(message, error: error, stackTrace: stackTrace);
    if (data != null) {
      logger.d('Data: $data');
    }
  }

  /// Log info message
  static void info(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? data}) {
    logger.i(message, error: error, stackTrace: stackTrace);
    if (data != null) {
      logger.i('Data: $data');
    }
  }

  /// Log warning message
  static void warning(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? data}) {
    logger.w(message, error: error, stackTrace: stackTrace);
    if (data != null) {
      logger.w('Data: $data');
    }
  }

  /// Log error message
  static void error(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? data}) {
    logger.e(message, error: error, stackTrace: stackTrace);
    if (data != null) {
      logger.e('Data: $data');
    }
  }

  /// Log fatal error
  static void fatal(String message, {Object? error, StackTrace? stackTrace, Map<String, dynamic>? data}) {
    logger.f(message, error: error, stackTrace: stackTrace);
    if (data != null) {
      logger.f('Data: $data');
    }
  }

  /// Log network request
  static void networkRequest(String method, String url, {Map<String, dynamic>? headers, dynamic body}) {
    if (kDebugMode) {
      logger.d('🌐 $method $url');
      if (headers != null) {
        logger.d('Headers: $headers');
      }
      if (body != null) {
        logger.d('Body: $body');
      }
    }
  }

  /// Log network response
  static void networkResponse(String method, String url, int statusCode, {dynamic body, Duration? duration}) {
    final String emoji = statusCode >= 200 && statusCode < 300 ? '✅' : '❌';
    final String durationText = duration != null ? ' (${duration.inMilliseconds}ms)' : '';
    
    if (kDebugMode) {
      logger.d('$emoji $method $url - $statusCode$durationText');
      if (body != null && statusCode >= 400) {
        logger.d('Error Body: $body');
      }
    } else if (statusCode >= 400) {
      logger.w('Network Error: $method $url - $statusCode$durationText');
    }
  }

  /// Log user action
  static void userAction(String action, {Map<String, dynamic>? data}) {
    logger.i('👤 User Action: $action', error: null, stackTrace: null);
    if (data != null) {
      logger.i('Action Data: $data');
    }
  }

  /// Log performance metric
  static void performance(String operation, Duration duration, {Map<String, dynamic>? data}) {
    final String emoji = duration.inMilliseconds > 1000 ? '🐌' : '⚡';
    logger.i('$emoji Performance: $operation took ${duration.inMilliseconds}ms');
    if (data != null) {
      logger.i('Performance Data: $data');
    }
  }

  /// Get log file path (for sharing logs)
  static String? get logFilePath => kReleaseMode ? _logFilePath : null;

  /// Clear old log files
  static Future<void> clearOldLogs({int keepDays = 7}) async {
    if (!kReleaseMode) return;
    
    try {
      final Directory appDocDir = await getApplicationDocumentsDirectory();
      final Directory logDir = Directory('${appDocDir.path}/logs');
      
      if (await logDir.exists()) {
        final List<FileSystemEntity> files = logDir.listSync();
        final DateTime cutoffDate = DateTime.now().subtract(Duration(days: keepDays));
        
        for (final FileSystemEntity file in files) {
          if (file is File) {
            final FileStat stat = await file.stat();
            if (stat.modified.isBefore(cutoffDate)) {
              await file.delete();
              logger.d('Deleted old log file: ${file.path}');
            }
          }
        }
      }
    } catch (e) {
      logger.e('Failed to clear old logs: $e');
    }
  }
}

/// Custom log filter
class _LogFilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    // In release mode, only log info and above
    if (kReleaseMode) {
      return event.level.index >= Level.info.index;
    }
    
    // In debug mode, log everything
    return true;
  }
}

/// Development printer with colors and emojis
class _DevelopmentPrinter extends LogPrinter {
  static final Map<Level, String> _levelEmojis = {
    Level.debug: '🐛',
    Level.info: 'ℹ️',
    Level.warning: '⚠️',
    Level.error: '❌',
    Level.fatal: '💀',
  };

  @override
  List<String> log(LogEvent event) {
    final String emoji = _levelEmojis[event.level] ?? '';
    final String timestamp = DateTime.now().toIso8601String();
    final String level = event.level.name.toUpperCase();
    
    final List<String> output = [];
    
    // Main message
    output.add('$emoji [$level] $timestamp: ${event.message}');
    
    // Error details
    if (event.error != null) {
      output.add('Error: ${event.error}');
    }
    
    // Stack trace (only for errors and fatal)
    if (event.stackTrace != null && event.level.index >= Level.error.index) {
      output.addAll(event.stackTrace.toString().split('\n'));
    }
    
    return output;
  }
}

/// Production printer (minimal, structured)
class _ProductionPrinter extends LogPrinter {
  @override
  List<String> log(LogEvent event) {
    final String timestamp = DateTime.now().toIso8601String();
    final String level = event.level.name.toUpperCase();
    
    final Map<String, dynamic> logEntry = {
      'timestamp': timestamp,
      'level': level,
      'message': event.message,
      if (event.error != null) 'error': event.error.toString(),
      if (event.stackTrace != null) 'stackTrace': event.stackTrace.toString(),
    };
    
    return [logEntry.toString()];
  }
}

/// File output for production logging
class _FileOutput extends LogOutput {
  @override
  void output(OutputEvent event) {
    if (AppLogger.logFilePath != null) {
      try {
        final File logFile = File(AppLogger.logFilePath!);
        final String logEntry = '${event.lines.join('\n')}\n';
        logFile.writeAsStringSync(logEntry, mode: FileMode.append);
      } catch (e) {
        debugPrint('Failed to write to log file: $e');
      }
    }
  }
}

/// Mixin for classes that need logging
mixin LoggerMixin {
  Logger get logger => AppLogger.logger;
  
  void logDebug(String message, {Object? error, StackTrace? stackTrace}) {
    AppLogger.debug('${runtimeType.toString()}: $message', error: error, stackTrace: stackTrace);
  }
  
  void logInfo(String message, {Object? error, StackTrace? stackTrace}) {
    AppLogger.info('${runtimeType.toString()}: $message', error: error, stackTrace: stackTrace);
  }
  
  void logWarning(String message, {Object? error, StackTrace? stackTrace}) {
    AppLogger.warning('${runtimeType.toString()}: $message', error: error, stackTrace: stackTrace);
  }
  
  void logError(String message, {Object? error, StackTrace? stackTrace}) {
    AppLogger.error('${runtimeType.toString()}: $message', error: error, stackTrace: stackTrace);
  }
}
