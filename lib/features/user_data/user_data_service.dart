import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sqflite/sqflite.dart';

import '../../data/models/user.dart';
import '../../data/models/user_data_models.dart';
import '../../data/sqlite/sqlite_helper.dart';
import '../../di/components/service_locator.dart';
import '../auth/providers/user_provider.dart';

class UserDataService {
  factory UserDataService() {
    return _instance;
  }

  UserDataService._internal();
  final SQLiteHelper _sqliteHelper = getIt<SQLiteHelper>();
  static const String _anonymousUserId = 'anonymous_user';

  // Singleton pattern
  static final UserDataService _instance = UserDataService._internal();

  // Get current user ID (authenticated or anonymous)
  Future<String> _getCurrentUserId(WidgetRef? ref) async {
    if (ref != null) {
      final AsyncValue<UserModel?> userState = ref.read(userNotifierProvider);
      if (userState.hasValue && userState.value != null) {
        return userState.value!.id;
      }
    }

    // Use anonymous user ID if not logged in
    return _anonymousUserId;
  }

  // Favorites methods
  Future<bool> toggleFavorite({
    required String itemId,
    required String type,
    WidgetRef? ref,
  }) async {
    final String userId = await _getCurrentUserId(ref);

    // Check if already favorited
    final bool isFavorited = await _sqliteHelper.isFavorite(
      userId: userId,
      itemId: itemId,
    );

    if (isFavorited) {
      // Remove from favorites
      await _sqliteHelper.removeFavorite(
        userId: userId,
        itemId: itemId,
      );
      return false;
    } else {
      // Add to favorites
      await _sqliteHelper.addFavorite(
        userId: userId,
        itemId: itemId,
        type: type,
      );
      return true;
    }
  }

  Future<bool> isFavorite({
    required String itemId,
    WidgetRef? ref,
  }) async {
    final String userId = await _getCurrentUserId(ref);

    return _sqliteHelper.isFavorite(
      userId: userId,
      itemId: itemId,
    );
  }

  Future<List<FavoriteItem>> getFavorites({
    WidgetRef? ref,
  }) async {
    final String userId = await _getCurrentUserId(ref);

    return _sqliteHelper.getUserFavorites(userId);
  }

  // Progress methods
  Future<void> saveProgress({
    required String itemId,
    required double positionSeconds,
    WidgetRef? ref,
  }) async {
    final String userId = await _getCurrentUserId(ref);

    await _sqliteHelper.saveProgress(
      userId: userId,
      itemId: itemId,
      positionSeconds: positionSeconds,
    );
  }

  Future<double?> getProgress({
    required String itemId,
    WidgetRef? ref,
  }) async {
    final String userId = await _getCurrentUserId(ref);

    final ProgressItem? progress = await _sqliteHelper.getProgress(
      userId: userId,
      itemId: itemId,
    );

    return progress?.positionSeconds;
  }

  Future<List<ProgressItem>> getAllProgress({
    WidgetRef? ref,
  }) async {
    final String userId = await _getCurrentUserId(ref);

    return _sqliteHelper.getUserProgress(userId);
  }

  // History methods
  Future<void> addToHistory({
    required String itemId,
    required String actionType,
    WidgetRef? ref,
  }) async {
    final String userId = await _getCurrentUserId(ref);

    await _sqliteHelper.addHistoryItem(
      userId: userId,
      itemId: itemId,
      actionType: actionType,
    );
  }

  Future<List<HistoryItem>> getHistory({
    WidgetRef? ref,
  }) async {
    final String userId = await _getCurrentUserId(ref);

    return _sqliteHelper.getUserHistory(userId);
  }

  // Clear user data
  Future<void> clearUserData({
    WidgetRef? ref,
    bool clearFavorites = true,
    bool clearProgress = true,
    bool clearHistory = true,
  }) async {
    final String userId = await _getCurrentUserId(ref);
    final Database db = await _sqliteHelper.database;

    if (clearFavorites) {
      await db.delete(
        'favorites',
        where: 'user_id = ?',
        whereArgs: <Object?>[userId],
      );
    }

    if (clearProgress) {
      await db.delete(
        'progress',
        where: 'user_id = ?',
        whereArgs: <Object?>[userId],
      );
    }

    if (clearHistory) {
      await db.delete(
        'history',
        where: 'user_id = ?',
        whereArgs: <Object?>[userId],
      );
    }
  }

  // Transfer anonymous data to user account
  Future<void> transferAnonymousData({
    required String userId,
  }) async {
    final Database db = await _sqliteHelper.database;

    // Update favorites
    await db.update(
      'favorites',
      <String, Object?>{'user_id': userId},
      where: 'user_id = ?',
      whereArgs: <Object?>[_anonymousUserId],
    );

    // Update progress
    await db.update(
      'progress',
      <String, Object?>{'user_id': userId},
      where: 'user_id = ?',
      whereArgs: <Object?>[_anonymousUserId],
    );

    // Update history
    await db.update(
      'history',
      <String, Object?>{'user_id': userId},
      where: 'user_id = ?',
      whereArgs: <Object?>[_anonymousUserId],
    );
  }
}
