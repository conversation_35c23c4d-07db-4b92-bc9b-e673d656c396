import 'package:flutter/material.dart';

import '../../../data/models/media_ui_model.dart';
import '../../../utils/icons_changer.dart';

/// Reusable component for displaying media title
class MediaTitle extends StatelessWidget {
  const MediaTitle({
    super.key,
    required this.title,
    this.style,
    this.maxLines = 2,
    this.overflow = TextOverflow.ellipsis,
  });

  final String title;
  final TextStyle? style;
  final int maxLines;
  final TextOverflow overflow;

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      style: style ?? Theme.of(context).textTheme.titleMedium,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

/// Reusable component for displaying media description
class MediaDescription extends StatelessWidget {
  const MediaDescription({
    super.key,
    required this.description,
    this.style,
    this.maxLines = 2,
    this.overflow = TextOverflow.ellipsis,
    this.padding,
  });

  final String description;
  final TextStyle? style;
  final int maxLines;
  final TextOverflow overflow;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Text(
        description,
        style: style ?? Theme.of(context).textTheme.bodySmall,
        maxLines: maxLines,
        overflow: overflow,
      ),
    );
  }
}

/// Reusable component for displaying media category
class MediaCategory extends StatelessWidget {
  const MediaCategory({
    super.key,
    required this.category,
    this.style,
    this.maxLines = 1,
    this.overflow = TextOverflow.ellipsis,
  });

  final String category;
  final TextStyle? style;
  final int maxLines;
  final TextOverflow overflow;

  @override
  Widget build(BuildContext context) {
    return Text(
      category,
      style: style ?? Theme.of(context).textTheme.bodySmall,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

/// Reusable component for displaying media thumbnail
class MediaThumbnail extends StatelessWidget {
  const MediaThumbnail({
    super.key,
    required this.thumbnailUrl,
    required this.fallbackIcon,
    this.width = 50,
    this.height = 50,
    this.fit = BoxFit.cover,
    this.borderRadius,
  });

  final String? thumbnailUrl;
  final IconData fallbackIcon;
  final double width;
  final double height;
  final BoxFit fit;
  final BorderRadius? borderRadius;

  @override
  Widget build(BuildContext context) {
    final Widget child = thumbnailUrl != null
        ? Image.network(
            thumbnailUrl!,
            width: width,
            height: height,
            fit: fit,
            errorBuilder: (_, __, ___) => Icon(fallbackIcon, size: width),
          )
        : Icon(fallbackIcon, size: width);

    if (borderRadius != null) {
      return ClipRRect(
        borderRadius: borderRadius!,
        child: child,
      );
    }

    return child;
  }
}

/// Reusable component for displaying duration info
class DurationInfo extends StatelessWidget {
  const DurationInfo({
    super.key,
    required this.durationSeconds,
    this.iconSize = 14,
    this.textStyle,
  });

  final int? durationSeconds;
  final double iconSize;
  final TextStyle? textStyle;

  @override
  Widget build(BuildContext context) {
    final String formattedDuration = _formatDuration();
    final TextStyle defaultStyle = TextStyle(
      color: Colors.grey[600],
      fontSize: 12,
    );

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Icon(Icons.access_time, size: iconSize, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Text(
          formattedDuration,
          style: textStyle ?? defaultStyle,
        ),
      ],
    );
  }

  String _formatDuration() {
    if (durationSeconds == null) return 'Unknown';
    final Duration duration = Duration(seconds: durationSeconds!);
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final String minutes = twoDigits(duration.inMinutes.remainder(60));
    final String seconds = twoDigits(duration.inSeconds.remainder(60));
    return duration.inHours > 0
        ? '${duration.inHours}:$minutes:$seconds'
        : '$minutes:$seconds';
  }
}

/// Reusable component for displaying date info
class DateInfo extends StatelessWidget {
  const DateInfo({
    super.key,
    required this.date,
    this.iconSize = 14,
    this.textStyle,
  });

  final DateTime? date;
  final double iconSize;
  final TextStyle? textStyle;

  @override
  Widget build(BuildContext context) {
    final String formattedDate = _formatDate();
    final TextStyle defaultStyle = TextStyle(
      color: Colors.grey[600],
      fontSize: 12,
    );

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Icon(Icons.calendar_today, size: iconSize, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Text(
          formattedDate,
          style: textStyle ?? defaultStyle,
        ),
      ],
    );
  }

  String _formatDate() {
    final DateTime actualDate = date ?? DateTime.now();
    return '${actualDate.day}/${actualDate.month}/${actualDate.year}';
  }
}

/// Reusable component for displaying pages info
class PagesInfo extends StatelessWidget {
  const PagesInfo({
    super.key,
    required this.pages,
    this.iconSize = 14,
    this.textStyle,
  });

  final int? pages;
  final double iconSize;
  final TextStyle? textStyle;

  @override
  Widget build(BuildContext context) {
    final String pagesText = pages != null ? '$pages pages' : 'Document';
    final TextStyle defaultStyle = TextStyle(
      color: Colors.grey[600],
      fontSize: 12,
    );

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Icon(Icons.insert_drive_file, size: iconSize, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Text(
          pagesText,
          style: textStyle ?? defaultStyle,
        ),
      ],
    );
  }
}

/// Reusable component for displaying media info row
class MediaInfoRow extends StatelessWidget {
  const MediaInfoRow({
    super.key,
    required this.item,
    this.showPages = false,
    this.spacing = 16.0,
  });

  final MediaUiModel item;
  final bool showPages;
  final double spacing;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        if (showPages) ...<Widget>[
          PagesInfo(pages: item.metadata?.additionalInfo?['pages'] as int?),
          SizedBox(width: spacing),
        ] else ...<Widget>[
          DurationInfo(durationSeconds: item.durationSeconds),
          SizedBox(width: spacing),
        ],
        DateInfo(date: item.createdAt),
      ],
    );
  }
}

/// Reusable component for document icon container
class DocumentIconContainer extends StatelessWidget {
  const DocumentIconContainer({
    super.key,
    required this.item,
    this.width = 60,
    this.height = 80,
    this.iconSize = 40,
  });

  final MediaUiModel item;
  final double width;
  final double height;
  final double iconSize;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(4),
        boxShadow: const <BoxShadow>[
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: item.thumbnailUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: Image.network(
                item.thumbnailUrl!,
                fit: BoxFit.cover,
                errorBuilder: (_, __, ___) =>
                    Icon(Icons.picture_as_pdf, size: iconSize),
              ),
            )
          : Icon(
              Icons.picture_as_pdf,
              size: iconSize,
              color: Theme.of(context).colorScheme.primary,
            ),
    );
  }
}

/// Reusable component for video card content
class VideoCardContent extends StatelessWidget {
  const VideoCardContent({
    super.key,
    required this.item,
  });

  final MediaUiModel item;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        // Video thumbnail
        AspectRatio(
          aspectRatio: 16 / 9,
          child: Stack(
            children: <Widget>[
              MediaThumbnail(
                thumbnailUrl: item.thumbnailUrl,
                fallbackIcon: Icons.video_library,
                width: double.infinity,
                height: double.infinity,
              ),
              // Play button overlay
              Center(
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: const Icon(
                    Icons.play_arrow,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
              ),
            ],
          ),
        ),
        // Details
        Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              MediaTitle(title: item.title),
              if (item.metadata?.description != null) ...<Widget>[
                const SizedBox(height: 4),
                MediaDescription(description: item.metadata!.description!),
              ],
              const SizedBox(height: 8),
              MediaInfoRow(item: item),
            ],
          ),
        ),
      ],
    );
  }
}

/// Reusable component for audio card content
class AudioCardContent extends StatelessWidget {
  const AudioCardContent({
    super.key,
    required this.item,
  });

  final MediaUiModel item;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Row(
        children: <Widget>[
          // Audio icon
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(8),
            ),
            child: MediaThumbnail(
              thumbnailUrl: item.thumbnailUrl,
              fallbackIcon: Icons.audiotrack,
              width: 40,
              height: 40,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                MediaTitle(
                  title: item.title,
                  maxLines: 1,
                ),
                const SizedBox(height: 4),
                MediaCategory(category: item.category),
                const SizedBox(height: 4),
                DurationInfo(durationSeconds: item.durationSeconds),
              ],
            ),
          ),
          Icon(
            Icons.play_circle_outline,
            color: Theme.of(context).colorScheme.primary,
          ),
        ],
      ),
    );
  }
}

/// Reusable component for document card content
class DocumentCardContent extends StatelessWidget {
  const DocumentCardContent({
    super.key,
    required this.item,
  });

  final MediaUiModel item;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              DocumentIconContainer(item: item),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    MediaTitle(title: item.title),
                    const SizedBox(height: 8),
                    MediaInfoRow(item: item, showPages: true),
                  ],
                ),
              ),
            ],
          ),
        ),
        if (item.metadata?.description != null)
          MediaDescription(
            description: item.metadata!.description!,
            padding: const EdgeInsets.fromLTRB(12.0, 0, 12.0, 12.0),
          ),
      ],
    );
  }
}

/// Reusable component for default card content
class DefaultCardContent extends StatelessWidget {
  const DefaultCardContent({
    super.key,
    required this.item,
    required this.mediaType,
  });

  final MediaUiModel item;
  final String mediaType;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: MediaTitle(title: item.title),
      subtitle: MediaCategory(category: item.category),
      leading: MediaThumbnail(
        thumbnailUrl: item.thumbnailUrl,
        fallbackIcon: getIconForType(mediaType),
      ),
    );
  }
}
