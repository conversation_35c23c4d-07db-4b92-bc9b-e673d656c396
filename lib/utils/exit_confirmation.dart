import 'package:flutter/material.dart';

Future<bool> showExitConfirmation(BuildContext context) async {
  return await showDialog<bool>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('تأكيد الخروج'),
            content: const Text('هل تريد الخروج من التطبيق؟'),
            actions: <Widget>[
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('خروج'),
              ),
            ],
          );
        },
      ) ??
      false; // If dialog is dismissed, don't exit
}

/// A widget that handles back button presses and shows a confirmation dialog
/// when the user tries to exit the app
class ExitConfirmationWrapper extends StatelessWidget {
  const ExitConfirmationWrapper({
    required this.child,
    this.shouldConfirmExit,
    super.key,
  });
  final Widget child;
  final bool Function()? shouldConfirmExit;

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // Check if we should show the confirmation dialog
        if (shouldConfirmExit != null && !shouldConfirmExit!()) {
          return true; // No confirmation needed, just exit
        }

        // Show confirmation dialog
        final bool shouldExit = await showExitConfirmation(context);
        if (shouldExit) {
          // For GoRouter compatibility, we should return true to allow the navigation
          // This will let GoRouter handle the navigation stack properly
          return true;
        }
        return false; // We handle the exit ourselves
      },
      child: child,
    );
  }
}
