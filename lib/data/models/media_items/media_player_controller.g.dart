// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'media_player_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(MediaPlayerController)
const mediaPlayerControllerProvider = MediaPlayerControllerFamily._();

final class MediaPlayerControllerProvider
    extends $NotifierProvider<MediaPlayerController, MediaPlayerState> {
  const MediaPlayerControllerProvider._(
      {required MediaPlayerControllerFamily super.from,
      required String super.argument})
      : super(
          retry: null,
          name: r'mediaPlayerControllerProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$mediaPlayerControllerHash();

  @override
  String toString() {
    return r'mediaPlayerControllerProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  MediaPlayerController create() => MediaPlayerController();

  @$internal
  @override
  $NotifierProviderElement<MediaPlayerController, MediaPlayerState>
      $createElement($ProviderPointer pointer) =>
          $NotifierProviderElement(pointer);

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(MediaPlayerState value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<MediaPlayerState>(value),
    );
  }

  @override
  bool operator ==(Object other) {
    return other is MediaPlayerControllerProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$mediaPlayerControllerHash() =>
    r'f42b954a2641b2eb368b6baecae8896f6f782089';

final class MediaPlayerControllerFamily extends $Family
    with
        $ClassFamilyOverride<MediaPlayerController, MediaPlayerState,
            MediaPlayerState, MediaPlayerState, String> {
  const MediaPlayerControllerFamily._()
      : super(
          retry: null,
          name: r'mediaPlayerControllerProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  MediaPlayerControllerProvider call(
    String mediaId,
  ) =>
      MediaPlayerControllerProvider._(argument: mediaId, from: this);

  @override
  String toString() => r'mediaPlayerControllerProvider';
}

abstract class _$MediaPlayerController extends $Notifier<MediaPlayerState> {
  late final _$args = ref.$arg as String;
  String get mediaId => _$args;

  MediaPlayerState build(
    String mediaId,
  );
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(
      _$args,
    );
    final ref = this.ref as $Ref<MediaPlayerState>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<MediaPlayerState>, MediaPlayerState, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

@ProviderFor(mediaItemDetails)
const mediaItemDetailsProvider = MediaItemDetailsFamily._();

final class MediaItemDetailsProvider
    extends $FunctionalProvider<MediaUiModel?, MediaUiModel?>
    with $Provider<MediaUiModel?> {
  const MediaItemDetailsProvider._(
      {required MediaItemDetailsFamily super.from,
      required String super.argument})
      : super(
          retry: null,
          name: r'mediaItemDetailsProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$mediaItemDetailsHash();

  @override
  String toString() {
    return r'mediaItemDetailsProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  $ProviderElement<MediaUiModel?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  MediaUiModel? create(Ref ref) {
    final argument = this.argument as String;
    return mediaItemDetails(
      ref,
      argument,
    );
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(MediaUiModel? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $ValueProvider<MediaUiModel?>(value),
    );
  }

  @override
  bool operator ==(Object other) {
    return other is MediaItemDetailsProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$mediaItemDetailsHash() => r'de300b027ff8c8c1093b2b6b09481b7b6c7ec8d3';

final class MediaItemDetailsFamily extends $Family
    with $FunctionalFamilyOverride<MediaUiModel?, String> {
  const MediaItemDetailsFamily._()
      : super(
          retry: null,
          name: r'mediaItemDetailsProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  MediaItemDetailsProvider call(
    String mediaId,
  ) =>
      MediaItemDetailsProvider._(argument: mediaId, from: this);

  @override
  String toString() => r'mediaItemDetailsProvider';
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
