// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'media_ui_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MediaUiModel _$MediaUiModelFromJson(Map<String, dynamic> json) =>
    _MediaUiModel(
      id: json['id'] as String,
      title: json['title'] as String,
      type: json['type'] as String,
      category: json['category'] as String,
      description: json['description'] as String?,
      thumbnailUrl: json['thumbnailUrl'] as String?,
      audioUrl: json['audioUrl'] as String?,
      videoUrl: json['videoUrl'] as String?,
      documentUrl: json['documentUrl'] as String?,
      articleText: json['articleText'] as String?,
      htmlContent: json['htmlContent'] as String?,
      tweetContent: json['tweetContent'] as String?,
      tweetAuthor: json['tweetAuthor'] as String?,
      tweetDate: json['tweetDate'] == null
          ? null
          : DateTime.parse(json['tweetDate'] as String),
      tweetPostLink: json['tweetPostLink'] as String?,
      tweetImageUrl: json['tweetImageUrl'] as String?,
      tweetUserId: json['tweetUserId'] as String?,
      tweetUserPageLink: json['tweetUserPageLink'] as String?,
      metadata: const MediaMetadataConverter()
          .fromJson(json['metadata'] as Map<String, dynamic>?),
      options: (json['options'] as List<dynamic>?)
          ?.map((e) => MediaOption.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$MediaUiModelToJson(_MediaUiModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'type': instance.type,
      'category': instance.category,
      'description': instance.description,
      'thumbnailUrl': instance.thumbnailUrl,
      'audioUrl': instance.audioUrl,
      'videoUrl': instance.videoUrl,
      'documentUrl': instance.documentUrl,
      'articleText': instance.articleText,
      'htmlContent': instance.htmlContent,
      'tweetContent': instance.tweetContent,
      'tweetAuthor': instance.tweetAuthor,
      'tweetDate': instance.tweetDate?.toIso8601String(),
      'tweetPostLink': instance.tweetPostLink,
      'tweetImageUrl': instance.tweetImageUrl,
      'tweetUserId': instance.tweetUserId,
      'tweetUserPageLink': instance.tweetUserPageLink,
      'metadata': const MediaMetadataConverter().toJson(instance.metadata),
      'options': instance.options,
    };
