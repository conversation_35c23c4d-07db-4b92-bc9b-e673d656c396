// import 'package:flutter/material.dart';
// import 'package:flutter_pdfview/flutter_pdfview.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:url_launcher/url_launcher.dart';

// import '../../data/models/media_items/media_player_controller.dart';
// import '../../data/models/media_player_state.dart';
// import '../../utils/exit_confirmation.dart';
// import '../navigation/navigation_provider.dart';
// import 'widgets/document_info_panel.dart';

// class TextMediaViewerScreen extends ConsumerWidget {
//   const TextMediaViewerScreen({
//     required this.mediaId,
//     super.key,
//   });

//   final String mediaId;

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     // Use the mediaPlayerControllerProvider to get the media player state
//     final MediaPlayerState mediaPlayerState =
//         ref.watch(mediaPlayerControllerProvider(mediaId));

//     // Check if the state is loading
//     if (mediaPlayerState.isLoading) {
//       return const Scaffold(
//         body: Center(child: CircularProgressIndicator()),
//       );
//     }

//     // Check if there's an error
//     if (mediaPlayerState.errorMessage != null) {
//       return ExitConfirmationWrapper(
//         shouldConfirmExit: () => true, // Always show confirmation
//         child: Scaffold(
//           appBar: AppBar(
//             title: const Text('Document Viewer Error'),
//             leading: IconButton(
//               icon: const Icon(Icons.arrow_back),
//               onPressed: () {
//                 // Use the navigation provider to handle back navigation
//                 ref.read(navigationProvider).safeGoBack(context);
//               },
//               tooltip: 'رجوع',
//             ),
//           ),
//           body: Center(
//             child: Column(
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: <Widget>[
//                 const Icon(
//                   Icons.error_outline,
//                   color: Colors.red,
//                   size: 60,
//                 ),
//                 const SizedBox(height: 16),
//                 const Text(
//                   'حدث خطأ أثناء تحميل المستند',
//                   style: TextStyle(fontSize: 18),
//                 ),
//                 const SizedBox(height: 8),
//                 Text(
//                   mediaPlayerState.errorMessage!,
//                   style: const TextStyle(color: Colors.grey),
//                   textAlign: TextAlign.center,
//                 ),
//                 const SizedBox(height: 16),
//                 ElevatedButton(
//                   onPressed: () {
//                     // Retry loading the document
//                     ref.invalidate(mediaPlayerControllerProvider(mediaId));
//                   },
//                   child: const Text('إعادة المحاولة'),
//                 ),
//               ],
//             ),
//           ),
//         ),
//       );
//     }

//     // Check if the media item is loaded
//     if (mediaPlayerState.mediaItem == null) {
//       return const Center(child: CircularProgressIndicator());
//     }

//     // Check if the document is a PDF file
//     final bool isPdfFile = mediaPlayerState.mediaItem!.documentUrl != null &&
//         mediaPlayerState.mediaItem!.documentUrl!.toLowerCase().endsWith('.pdf');

//     return ExitConfirmationWrapper(
//       shouldConfirmExit: () => true, // Always show confirmation
//       child: Scaffold(
//         appBar: AppBar(
//           title: Text(mediaPlayerState.mediaItem?.title ?? 'Document Viewer'),
//           leading: IconButton(
//             icon: const Icon(Icons.arrow_back),
//             onPressed: () {
//               // Use the navigation provider to handle back navigation
//               ref.read(navigationProvider).safeGoBack(context);
//             },
//             tooltip: 'رجوع',
//           ),
//           actions: <Widget>[
//             // PDF-specific controls
//             if (isPdfFile) ...<Widget>[
//               // Page navigation for PDFs
//               IconButton(
//                 icon: const Icon(Icons.info_outline),
//                 onPressed: () {
//                   showModalBottomSheet<void>(
//                     context: context,
//                     builder: (BuildContext context) {
//                       return DocumentInfoPanel(
//                           mediaItem: mediaPlayerState.mediaItem!);
//                     },
//                   );
//                 },
//                 tooltip: 'معلومات',
//               ),
//             ],

//             // Common controls for all document types
//             IconButton(
//               icon: const Icon(Icons.open_in_browser),
//               onPressed: () async {
//                 final String? url = mediaPlayerState.mediaItem?.documentUrl;
//                 if (url != null) {
//                   final Uri uri = Uri.parse(url);
//                   if (await canLaunchUrl(uri)) {
//                     await launchUrl(uri);
//                   }
//                 }
//               },
//               tooltip: 'فتح في المتصفح',
//             ),
//           ],
//         ),
//         body: _buildDocumentViewer(context, mediaPlayerState, isPdfFile),
//       ),
//     );
//   }

//   Widget _buildDocumentViewer(
//     BuildContext context,
//     MediaPlayerState mediaPlayerState,
//     bool isPdfFile,
//   ) {
//     try {
//       // If it's a PDF file, use the PDF viewer
//       if (isPdfFile) {
//         final String pdfUrl = mediaPlayerState.mediaItem!.documentUrl!;
//         return Column(
//           children: <Widget>[
//             Expanded(
//               child: PDFView(
//                 filePath: pdfUrl,
//                 swipeHorizontal: true,
//                 autoSpacing: false,
//                 pageFling: false,
//                 onError: (error) {
//                   debugPrint('Error loading PDF: $error');
//                 },
//                 onPageError: (int? page, error) {
//                   debugPrint('Error loading page $page: $error');
//                 },
//               ),
//             ),
//           ],
//         );
//       }

//       // For non-PDF documents, show the text content
//       return SingleChildScrollView(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: <Widget>[
//             // Document metadata
//             if (mediaPlayerState.mediaItem?.description != null &&
//                 mediaPlayerState.mediaItem!.description!.isNotEmpty)
//               Padding(
//                 padding: const EdgeInsets.only(bottom: 16.0),
//                 child: Text(
//                   'الوصف: ${mediaPlayerState.mediaItem!.description}',
//                   style: const TextStyle(
//                     fontWeight: FontWeight.bold,
//                     fontSize: 16,
//                   ),
//                 ),
//               ),

//             // Document content
//             if (mediaPlayerState.mediaItem?.articleText != null)
//               SelectableText(
//                 mediaPlayerState.mediaItem!.articleText!,
//                 style: const TextStyle(
//                   fontSize: 18,
//                   height: 1.5,
//                 ),
//               ),

//             // If no content is available
//             if (mediaPlayerState.mediaItem?.articleText == null ||
//                 mediaPlayerState.mediaItem!.articleText!.isEmpty)
//               const Center(
//                 child: Text(
//                   'لا يوجد محتوى متاح لهذا المستند',
//                   style: TextStyle(
//                     fontSize: 16,
//                     fontStyle: FontStyle.italic,
//                   ),
//                 ),
//               ),
//           ],
//         ),
//       );
//     } catch (e) {
//       // If there's an error, show a fallback UI
//       return Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: <Widget>[
//             const Icon(
//               Icons.error_outline,
//               color: Colors.red,
//               size: 60,
//             ),
//             const SizedBox(height: 16),
//             const Text(
//               'حدث خطأ أثناء عرض المستند',
//               style: TextStyle(fontSize: 18),
//             ),
//             const SizedBox(height: 8),
//             Text(
//               e.toString(),
//               style: const TextStyle(color: Colors.grey),
//               textAlign: TextAlign.center,
//             ),
//           ],
//         ),
//       );
//     }
//   }
// }
