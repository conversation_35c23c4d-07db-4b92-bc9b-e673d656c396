import 'package:freezed_annotation/freezed_annotation.dart';

import 'media_item_converter.dart';
import 'media_metadata.dart';

part 'media_item.freezed.dart';
part 'media_item.g.dart';

@freezed
abstract class MediaItem with _$MediaItem {
  const factory MediaItem({
    required String id,
    required String slug,
    required String type,
    required String category,
    required String title,
    String? description,
    required String language,
    required String searchableText,
    required DateTime createdAt,
    required DateTime updatedAt,
    int? durationSeconds,
    String? thumbnailUrl,
    String? license,
    required List<MediaOption> options,
    @MediaMetadataConverter()
    MediaMetadata?
        metadata, // Using the new typed MediaMetadata class with converter
  }) = _MediaItem;

  factory MediaItem.fromJson(Map<String, dynamic> json) =>
      _$MediaItemFromJson(json);
}

@freezed
abstract class MediaOption with _$MediaOption {
  const factory MediaOption({
    required String optionId,
    required String format,
    String? url,
    String? description,
    String? role,
    int? bitrate,
    // HTML content field
    String? html,
    // New fields for tweets
    @<PERSON>son<PERSON>ey(name: 'post_link') String? postLink,
    @JsonKey(name: 'post_content') String? postContent,
    @JsonKey(name: 'user_info') Map<String, dynamic>? userInfo,
    @JsonKey(name: 'post_image') String? postImage,
  }) = _MediaOption;

  factory MediaOption.fromJson(Map<String, dynamic> json) =>
      _$MediaOptionFromJson(json);
}
