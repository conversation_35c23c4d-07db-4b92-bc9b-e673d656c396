// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'media_ui_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MediaUiModel {
  String get id;
  String get title;
  String get type;
  String get category;
  String? get thumbnailUrl;
  String? get audioUrl;
  String? get videoUrl;
  String? get documentUrl; // For PDF, DOC, etc.
  String? get articleText; // For article content
  String? get tweetContent; // For tweet content
  String? get tweetAuthor; // For tweet author
  DateTime? get tweetDate; // For tweet date
  String? get tweetPostLink; // Link to the original tweet
  String? get tweetImageUrl; // Image in the tweet
  String? get tweetUserId; // User ID of the tweet author
  String? get tweetUserPageLink; // Link to the author's profile
  @MediaMetadataConverter()
  MediaMetadata?
      get metadata; // Using the new typed MediaMetadata class with converter
  String? get description;
  List<MediaOption>? get options;

  /// Create a copy of MediaUiModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MediaUiModelCopyWith<MediaUiModel> get copyWith =>
      _$MediaUiModelCopyWithImpl<MediaUiModel>(
          this as MediaUiModel, _$identity);

  /// Serializes this MediaUiModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MediaUiModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.thumbnailUrl, thumbnailUrl) ||
                other.thumbnailUrl == thumbnailUrl) &&
            (identical(other.audioUrl, audioUrl) ||
                other.audioUrl == audioUrl) &&
            (identical(other.videoUrl, videoUrl) ||
                other.videoUrl == videoUrl) &&
            (identical(other.documentUrl, documentUrl) ||
                other.documentUrl == documentUrl) &&
            (identical(other.articleText, articleText) ||
                other.articleText == articleText) &&
            (identical(other.tweetContent, tweetContent) ||
                other.tweetContent == tweetContent) &&
            (identical(other.tweetAuthor, tweetAuthor) ||
                other.tweetAuthor == tweetAuthor) &&
            (identical(other.tweetDate, tweetDate) ||
                other.tweetDate == tweetDate) &&
            (identical(other.tweetPostLink, tweetPostLink) ||
                other.tweetPostLink == tweetPostLink) &&
            (identical(other.tweetImageUrl, tweetImageUrl) ||
                other.tweetImageUrl == tweetImageUrl) &&
            (identical(other.tweetUserId, tweetUserId) ||
                other.tweetUserId == tweetUserId) &&
            (identical(other.tweetUserPageLink, tweetUserPageLink) ||
                other.tweetUserPageLink == tweetUserPageLink) &&
            (identical(other.metadata, metadata) ||
                other.metadata == metadata) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality().equals(other.options, options));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        title,
        type,
        category,
        thumbnailUrl,
        audioUrl,
        videoUrl,
        documentUrl,
        articleText,
        tweetContent,
        tweetAuthor,
        tweetDate,
        tweetPostLink,
        tweetImageUrl,
        tweetUserId,
        tweetUserPageLink,
        metadata,
        description,
        const DeepCollectionEquality().hash(options)
      ]);

  @override
  String toString() {
    return 'MediaUiModel(id: $id, title: $title, type: $type, category: $category, thumbnailUrl: $thumbnailUrl, audioUrl: $audioUrl, videoUrl: $videoUrl, documentUrl: $documentUrl, articleText: $articleText, tweetContent: $tweetContent, tweetAuthor: $tweetAuthor, tweetDate: $tweetDate, tweetPostLink: $tweetPostLink, tweetImageUrl: $tweetImageUrl, tweetUserId: $tweetUserId, tweetUserPageLink: $tweetUserPageLink, metadata: $metadata, description: $description, options: $options)';
  }
}

/// @nodoc
abstract mixin class $MediaUiModelCopyWith<$Res> {
  factory $MediaUiModelCopyWith(
          MediaUiModel value, $Res Function(MediaUiModel) _then) =
      _$MediaUiModelCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String title,
      String type,
      String category,
      String? thumbnailUrl,
      String? audioUrl,
      String? videoUrl,
      String? documentUrl,
      String? articleText,
      String? tweetContent,
      String? tweetAuthor,
      DateTime? tweetDate,
      String? tweetPostLink,
      String? tweetImageUrl,
      String? tweetUserId,
      String? tweetUserPageLink,
      @MediaMetadataConverter() MediaMetadata? metadata,
      String? description,
      List<MediaOption>? options});

  $MediaMetadataCopyWith<$Res>? get metadata;
}

/// @nodoc
class _$MediaUiModelCopyWithImpl<$Res> implements $MediaUiModelCopyWith<$Res> {
  _$MediaUiModelCopyWithImpl(this._self, this._then);

  final MediaUiModel _self;
  final $Res Function(MediaUiModel) _then;

  /// Create a copy of MediaUiModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? type = null,
    Object? category = null,
    Object? thumbnailUrl = freezed,
    Object? audioUrl = freezed,
    Object? videoUrl = freezed,
    Object? documentUrl = freezed,
    Object? articleText = freezed,
    Object? tweetContent = freezed,
    Object? tweetAuthor = freezed,
    Object? tweetDate = freezed,
    Object? tweetPostLink = freezed,
    Object? tweetImageUrl = freezed,
    Object? tweetUserId = freezed,
    Object? tweetUserPageLink = freezed,
    Object? metadata = freezed,
    Object? description = freezed,
    Object? options = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _self.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
      thumbnailUrl: freezed == thumbnailUrl
          ? _self.thumbnailUrl
          : thumbnailUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      audioUrl: freezed == audioUrl
          ? _self.audioUrl
          : audioUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      videoUrl: freezed == videoUrl
          ? _self.videoUrl
          : videoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      documentUrl: freezed == documentUrl
          ? _self.documentUrl
          : documentUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      articleText: freezed == articleText
          ? _self.articleText
          : articleText // ignore: cast_nullable_to_non_nullable
              as String?,
      tweetContent: freezed == tweetContent
          ? _self.tweetContent
          : tweetContent // ignore: cast_nullable_to_non_nullable
              as String?,
      tweetAuthor: freezed == tweetAuthor
          ? _self.tweetAuthor
          : tweetAuthor // ignore: cast_nullable_to_non_nullable
              as String?,
      tweetDate: freezed == tweetDate
          ? _self.tweetDate
          : tweetDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      tweetPostLink: freezed == tweetPostLink
          ? _self.tweetPostLink
          : tweetPostLink // ignore: cast_nullable_to_non_nullable
              as String?,
      tweetImageUrl: freezed == tweetImageUrl
          ? _self.tweetImageUrl
          : tweetImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      tweetUserId: freezed == tweetUserId
          ? _self.tweetUserId
          : tweetUserId // ignore: cast_nullable_to_non_nullable
              as String?,
      tweetUserPageLink: freezed == tweetUserPageLink
          ? _self.tweetUserPageLink
          : tweetUserPageLink // ignore: cast_nullable_to_non_nullable
              as String?,
      metadata: freezed == metadata
          ? _self.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as MediaMetadata?,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      options: freezed == options
          ? _self.options
          : options // ignore: cast_nullable_to_non_nullable
              as List<MediaOption>?,
    ));
  }

  /// Create a copy of MediaUiModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MediaMetadataCopyWith<$Res>? get metadata {
    if (_self.metadata == null) {
      return null;
    }

    return $MediaMetadataCopyWith<$Res>(_self.metadata!, (value) {
      return _then(_self.copyWith(metadata: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _MediaUiModel implements MediaUiModel {
  const _MediaUiModel(
      {required this.id,
      required this.title,
      required this.type,
      required this.category,
      this.thumbnailUrl,
      this.audioUrl,
      this.videoUrl,
      this.documentUrl,
      this.articleText,
      this.tweetContent,
      this.tweetAuthor,
      this.tweetDate,
      this.tweetPostLink,
      this.tweetImageUrl,
      this.tweetUserId,
      this.tweetUserPageLink,
      @MediaMetadataConverter() this.metadata,
      this.description,
      final List<MediaOption>? options})
      : _options = options;
  factory _MediaUiModel.fromJson(Map<String, dynamic> json) =>
      _$MediaUiModelFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String type;
  @override
  final String category;
  @override
  final String? thumbnailUrl;
  @override
  final String? audioUrl;
  @override
  final String? videoUrl;
  @override
  final String? documentUrl;
// For PDF, DOC, etc.
  @override
  final String? articleText;
// For article content
  @override
  final String? tweetContent;
// For tweet content
  @override
  final String? tweetAuthor;
// For tweet author
  @override
  final DateTime? tweetDate;
// For tweet date
  @override
  final String? tweetPostLink;
// Link to the original tweet
  @override
  final String? tweetImageUrl;
// Image in the tweet
  @override
  final String? tweetUserId;
// User ID of the tweet author
  @override
  final String? tweetUserPageLink;
// Link to the author's profile
  @override
  @MediaMetadataConverter()
  final MediaMetadata? metadata;
// Using the new typed MediaMetadata class with converter
  @override
  final String? description;
  final List<MediaOption>? _options;
  @override
  List<MediaOption>? get options {
    final value = _options;
    if (value == null) return null;
    if (_options is EqualUnmodifiableListView) return _options;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// Create a copy of MediaUiModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MediaUiModelCopyWith<_MediaUiModel> get copyWith =>
      __$MediaUiModelCopyWithImpl<_MediaUiModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MediaUiModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MediaUiModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.thumbnailUrl, thumbnailUrl) ||
                other.thumbnailUrl == thumbnailUrl) &&
            (identical(other.audioUrl, audioUrl) ||
                other.audioUrl == audioUrl) &&
            (identical(other.videoUrl, videoUrl) ||
                other.videoUrl == videoUrl) &&
            (identical(other.documentUrl, documentUrl) ||
                other.documentUrl == documentUrl) &&
            (identical(other.articleText, articleText) ||
                other.articleText == articleText) &&
            (identical(other.tweetContent, tweetContent) ||
                other.tweetContent == tweetContent) &&
            (identical(other.tweetAuthor, tweetAuthor) ||
                other.tweetAuthor == tweetAuthor) &&
            (identical(other.tweetDate, tweetDate) ||
                other.tweetDate == tweetDate) &&
            (identical(other.tweetPostLink, tweetPostLink) ||
                other.tweetPostLink == tweetPostLink) &&
            (identical(other.tweetImageUrl, tweetImageUrl) ||
                other.tweetImageUrl == tweetImageUrl) &&
            (identical(other.tweetUserId, tweetUserId) ||
                other.tweetUserId == tweetUserId) &&
            (identical(other.tweetUserPageLink, tweetUserPageLink) ||
                other.tweetUserPageLink == tweetUserPageLink) &&
            (identical(other.metadata, metadata) ||
                other.metadata == metadata) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality().equals(other._options, _options));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        title,
        type,
        category,
        thumbnailUrl,
        audioUrl,
        videoUrl,
        documentUrl,
        articleText,
        tweetContent,
        tweetAuthor,
        tweetDate,
        tweetPostLink,
        tweetImageUrl,
        tweetUserId,
        tweetUserPageLink,
        metadata,
        description,
        const DeepCollectionEquality().hash(_options)
      ]);

  @override
  String toString() {
    return 'MediaUiModel(id: $id, title: $title, type: $type, category: $category, thumbnailUrl: $thumbnailUrl, audioUrl: $audioUrl, videoUrl: $videoUrl, documentUrl: $documentUrl, articleText: $articleText, tweetContent: $tweetContent, tweetAuthor: $tweetAuthor, tweetDate: $tweetDate, tweetPostLink: $tweetPostLink, tweetImageUrl: $tweetImageUrl, tweetUserId: $tweetUserId, tweetUserPageLink: $tweetUserPageLink, metadata: $metadata, description: $description, options: $options)';
  }
}

/// @nodoc
abstract mixin class _$MediaUiModelCopyWith<$Res>
    implements $MediaUiModelCopyWith<$Res> {
  factory _$MediaUiModelCopyWith(
          _MediaUiModel value, $Res Function(_MediaUiModel) _then) =
      __$MediaUiModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String title,
      String type,
      String category,
      String? thumbnailUrl,
      String? audioUrl,
      String? videoUrl,
      String? documentUrl,
      String? articleText,
      String? tweetContent,
      String? tweetAuthor,
      DateTime? tweetDate,
      String? tweetPostLink,
      String? tweetImageUrl,
      String? tweetUserId,
      String? tweetUserPageLink,
      @MediaMetadataConverter() MediaMetadata? metadata,
      String? description,
      List<MediaOption>? options});

  @override
  $MediaMetadataCopyWith<$Res>? get metadata;
}

/// @nodoc
class __$MediaUiModelCopyWithImpl<$Res>
    implements _$MediaUiModelCopyWith<$Res> {
  __$MediaUiModelCopyWithImpl(this._self, this._then);

  final _MediaUiModel _self;
  final $Res Function(_MediaUiModel) _then;

  /// Create a copy of MediaUiModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? type = null,
    Object? category = null,
    Object? thumbnailUrl = freezed,
    Object? audioUrl = freezed,
    Object? videoUrl = freezed,
    Object? documentUrl = freezed,
    Object? articleText = freezed,
    Object? tweetContent = freezed,
    Object? tweetAuthor = freezed,
    Object? tweetDate = freezed,
    Object? tweetPostLink = freezed,
    Object? tweetImageUrl = freezed,
    Object? tweetUserId = freezed,
    Object? tweetUserPageLink = freezed,
    Object? metadata = freezed,
    Object? description = freezed,
    Object? options = freezed,
  }) {
    return _then(_MediaUiModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _self.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
      thumbnailUrl: freezed == thumbnailUrl
          ? _self.thumbnailUrl
          : thumbnailUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      audioUrl: freezed == audioUrl
          ? _self.audioUrl
          : audioUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      videoUrl: freezed == videoUrl
          ? _self.videoUrl
          : videoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      documentUrl: freezed == documentUrl
          ? _self.documentUrl
          : documentUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      articleText: freezed == articleText
          ? _self.articleText
          : articleText // ignore: cast_nullable_to_non_nullable
              as String?,
      tweetContent: freezed == tweetContent
          ? _self.tweetContent
          : tweetContent // ignore: cast_nullable_to_non_nullable
              as String?,
      tweetAuthor: freezed == tweetAuthor
          ? _self.tweetAuthor
          : tweetAuthor // ignore: cast_nullable_to_non_nullable
              as String?,
      tweetDate: freezed == tweetDate
          ? _self.tweetDate
          : tweetDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      tweetPostLink: freezed == tweetPostLink
          ? _self.tweetPostLink
          : tweetPostLink // ignore: cast_nullable_to_non_nullable
              as String?,
      tweetImageUrl: freezed == tweetImageUrl
          ? _self.tweetImageUrl
          : tweetImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      tweetUserId: freezed == tweetUserId
          ? _self.tweetUserId
          : tweetUserId // ignore: cast_nullable_to_non_nullable
              as String?,
      tweetUserPageLink: freezed == tweetUserPageLink
          ? _self.tweetUserPageLink
          : tweetUserPageLink // ignore: cast_nullable_to_non_nullable
              as String?,
      metadata: freezed == metadata
          ? _self.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as MediaMetadata?,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      options: freezed == options
          ? _self._options
          : options // ignore: cast_nullable_to_non_nullable
              as List<MediaOption>?,
    ));
  }

  /// Create a copy of MediaUiModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MediaMetadataCopyWith<$Res>? get metadata {
    if (_self.metadata == null) {
      return null;
    }

    return $MediaMetadataCopyWith<$Res>(_self.metadata!, (value) {
      return _then(_self.copyWith(metadata: value));
    });
  }
}

// dart format on
