import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';

import '../../data/enums/media_type_enum.dart';
import '../../data/models/media_items/media_provider.dart';
import '../../data/models/media_ui_model.dart';

class TweetsListScreen extends ConsumerWidget {
  const TweetsListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final List<MediaUiModel> allMedia = ref.watch(filteredMediaProvider);

    // Filter only tweet items
    final List<MediaUiModel> tweetItems = allMedia.where((MediaUiModel item) {
      final MediaType itemType = MediaType.fromString(item.type);
      final MediaCategory itemCategory =
          MediaCategory.fromString(item.category);
      return (itemType == MediaType.tweet) ||
          (itemType == MediaType.text && itemCategory == MediaCategory.tweet) ||
          (item.tweetContent != null && item.tweetContent!.isNotEmpty);
    }).toList();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Tweets'),
        centerTitle: true,
      ),
      body: tweetItems.isEmpty
          ? const Center(
              child: Text('No tweets available'),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: tweetItems.length,
              itemBuilder: (BuildContext context, int index) {
                final MediaUiModel tweet = tweetItems[index];
                return TweetCard(tweet: tweet);
              },
            ),
    );
  }
}

class TweetCard extends StatelessWidget {
  const TweetCard({
    required this.tweet,
    super.key,
  });
  final MediaUiModel tweet;

  @override
  Widget build(BuildContext context) {
    // Extract tweet content from the model
    final String tweetContent = tweet.tweetContent ?? tweet.title;
    final String tweetAuthor = tweet.tweetAuthor ?? 'Dr. Al Farih';
    final String tweetHandle = '@${tweet.tweetUserId ?? 'dralfarih'}';
    final String? tweetPostLink = tweet.tweetPostLink;
    // Use tweetImageUrl if available, otherwise fall back to thumbnailUrl
    final String? tweetImageUrl = tweet.tweetImageUrl ?? tweet.thumbnailUrl;

    // Get date
    final DateTime? tweetDate = tweet.tweetDate ??
        DateTime.tryParse(tweet.metadata?.publishDate as String? ?? '');

    // Format the date if available
    String formattedDate = '';
    if (tweetDate != null) {
      formattedDate = DateFormat('h:mm a · MMM d, yyyy').format(tweetDate);
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          debugPrint('Tweet card tapped for ID: ${tweet.id}');
          // Use named navigation for more reliable routing
          debugPrint('Using named navigation to tweetDetails');
          context.pushNamed('tweetDetails',
              pathParameters: <String, String>{'id': tweet.id});
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // Tweet header
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // Profile picture
                  CircleAvatar(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    radius: 24,
                    child: Text(
                      tweetAuthor.isNotEmpty
                          ? tweetAuthor[0].toUpperCase()
                          : 'D',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Author info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Row(
                          children: <Widget>[
                            Text(
                              tweetAuthor,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Icon(
                              Icons.verified,
                              color: Colors.blue[400],
                              size: 16,
                            ),
                          ],
                        ),
                        Text(
                          tweetHandle,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // X logo
                  Image.network(
                    'https://upload.wikimedia.org/wikipedia/commons/5/57/X_logo_2023_%28white%29.png',
                    width: 20,
                    height: 20,
                    color: Colors.grey[800],
                    errorBuilder: (BuildContext context, Object error,
                            StackTrace? stackTrace) =>
                        Icon(Icons.more_horiz, color: Colors.grey[800]),
                  ),
                ],
              ),
            ),

            // Tweet content with highlighted hashtags and mentions
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: _buildRichText(context, tweetContent),
            ),

            // Tweet image if available
            if (tweetImageUrl != null && tweetImageUrl.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 12),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.network(
                    tweetImageUrl,
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: 200,
                    errorBuilder: (BuildContext context, Object error,
                            StackTrace? stackTrace) =>
                        Container(
                      width: double.infinity,
                      height: 200,
                      color: Colors.grey[300],
                      child: const Center(
                        child: Icon(Icons.image_not_supported,
                            size: 50, color: Colors.grey),
                      ),
                    ),
                  ),
                ),
              ),

            // Tweet date
            if (formattedDate.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(left: 16, right: 16, top: 12),
                child: Text(
                  formattedDate,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ),

            // Divider
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Divider(color: Colors.grey[300]),
            ),

            // Tweet actions
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  // Comment (disabled)
                  Row(
                    children: <Widget>[
                      Icon(
                        Icons.chat_bubble_outline,
                        size: 18,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '0',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),

                  // Retweet (disabled)
                  Row(
                    children: <Widget>[
                      Icon(
                        Icons.repeat,
                        size: 18,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '0',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),

                  // Like (disabled)
                  Row(
                    children: <Widget>[
                      Icon(
                        Icons.favorite_border,
                        size: 18,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '0',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),

                  // Share
                  GestureDetector(
                    onTap: () => _shareTweet(
                        context, tweetContent, tweetAuthor, tweetPostLink),
                    child: Row(
                      children: <Widget>[
                        Icon(
                          Icons.share,
                          size: 18,
                          color: Colors.grey[600],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _shareTweet(
      BuildContext context, String content, String author, String? postLink) {
    String shareText;

    if (postLink != null && postLink.isNotEmpty) {
      // If we have a post link, share that
      shareText = postLink;
    } else {
      // Otherwise share the content and author
      shareText = '"$content"\n\n- $author';
    }

    SharePlus.instance.share(ShareParams(text: shareText));
  }

  // Helper method to build rich text with highlighted hashtags and mentions
  Widget _buildRichText(BuildContext context, String text) {
    // Regular expressions for hashtags and mentions
    final RegExp hashtagRegExp = RegExp(r'\B#\w+');
    final RegExp mentionRegExp = RegExp(r'\B@\w+');

    // Find all hashtags and mentions
    final List<RegExpMatch> hashtagMatches =
        hashtagRegExp.allMatches(text).toList();
    final List<RegExpMatch> mentionMatches =
        mentionRegExp.allMatches(text).toList();

    // Combine all matches and sort by position
    final List<RegExpMatch> allMatches = <RegExpMatch>[
      ...hashtagMatches,
      ...mentionMatches
    ];
    allMatches
        .sort((RegExpMatch a, RegExpMatch b) => a.start.compareTo(b.start));

    // If no matches, return simple text
    if (allMatches.isEmpty) {
      return Text(
        text,
        style: const TextStyle(
          fontSize: 16,
          height: 1.4,
        ),
      );
    }

    // Build rich text with spans
    final List<TextSpan> spans = <TextSpan>[];
    int lastEnd = 0;

    for (final RegExpMatch match in allMatches) {
      // Add text before the match
      if (match.start > lastEnd) {
        spans.add(TextSpan(
          text: text.substring(lastEnd, match.start),
          style: const TextStyle(
            fontSize: 16,
            height: 1.4,
          ),
        ));
      }

      // Add the hashtag or mention with color
      final String matchText = text.substring(match.start, match.end);
      final bool isHashtag = matchText.startsWith('#');

      spans.add(TextSpan(
        text: matchText,
        style: TextStyle(
          fontSize: 16,
          height: 1.4,
          color: isHashtag ? Colors.blue : Colors.blue,
          fontWeight: FontWeight.bold,
        ),
      ));

      lastEnd = match.end;
    }

    // Add any remaining text
    if (lastEnd < text.length) {
      spans.add(TextSpan(
        text: text.substring(lastEnd),
        style: const TextStyle(
          fontSize: 16,
          height: 1.4,
        ),
      ));
    }

    return RichText(
      text: TextSpan(
        children: spans,
        style: TextStyle(
          color: Theme.of(context).textTheme.bodyLarge?.color,
        ),
      ),
    );
  }
}
