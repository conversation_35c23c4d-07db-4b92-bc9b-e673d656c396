import 'package:flutter/material.dart';

/// Enum representing different types of media files that can be downloaded
enum MediaFileType {
  /// PDF document format
  pdf('pdf', 'application/pdf'),

  /// Microsoft Word document format
  docx('docx',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'),

  /// HTML document format
  html('html', 'text/html'),

  /// Plain text document format
  txt('txt', 'text/plain'),

  /// Audio file format
  audio('audio', 'audio/mpeg'),

  /// Video file format
  video('video', 'video/mp4');

  /// Constructor for MediaFileType
  const MediaFileType(this.extension, this.mimeType);

  /// File extension (e.g., 'pdf', 'docx')
  final String extension;

  /// MIME type (e.g., 'application/pdf')
  final String mimeType;

  /// Get a MediaFileType from a format string
  static MediaFileType? fromFormat(String format) {
    final String lowerFormat = format.toLowerCase();

    if (lowerFormat.contains('pdf') ||
        lowerFormat.contains('application/pdf')) {
      return MediaFileType.pdf;
    } else if (lowerFormat.contains('docx') ||
        lowerFormat.contains('msword') ||
        lowerFormat.contains(
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document')) {
      return MediaFileType.docx;
    } else if (lowerFormat.contains('html')) {
      return MediaFileType.html;
    } else if (lowerFormat.contains('text/plain') ||
        lowerFormat.contains('txt')) {
      return MediaFileType.txt;
    } else if (lowerFormat.startsWith('audio')) {
      return MediaFileType.audio;
    } else if (lowerFormat.startsWith('video')) {
      return MediaFileType.video;
    }

    return null;
  }

  /// Get the icon data for this file type
  IconData get icon {
    switch (this) {
      case MediaFileType.pdf:
        return Icons.picture_as_pdf_rounded;
      case MediaFileType.docx:
        return Icons.description_rounded;
      case MediaFileType.html:
        return Icons.html_rounded;
      case MediaFileType.txt:
        return Icons.text_snippet_rounded;
      case MediaFileType.audio:
        return Icons.audio_file_rounded;
      case MediaFileType.video:
        return Icons.video_file_rounded;
    }
  }

  /// Get the icon color for this file type
  Color get iconColor {
    switch (this) {
      case MediaFileType.pdf:
        return Colors.red.shade700;
      case MediaFileType.docx:
        return Colors.blue.shade700;
      case MediaFileType.html:
        return Colors.orange.shade700;
      case MediaFileType.txt:
        return Colors.grey.shade700;
      case MediaFileType.audio:
        return Colors.purple.shade700;
      case MediaFileType.video:
        return Colors.green.shade700;
    }
  }

  /// Get the display title for this file type
  String get displayTitle {
    switch (this) {
      case MediaFileType.pdf:
        return 'PDF Document';
      case MediaFileType.docx:
        return 'Word Document (DOCX)';
      case MediaFileType.html:
        return 'HTML Document';
      case MediaFileType.txt:
        return 'Text Document';
      case MediaFileType.audio:
        return 'Audio File';
      case MediaFileType.video:
        return 'Video File';
    }
  }

  /// Get the default subtitle for this file type
  String get defaultSubtitle {
    switch (this) {
      case MediaFileType.pdf:
        return 'Best for printing and viewing';
      case MediaFileType.docx:
        return 'Editable in Microsoft Word';
      case MediaFileType.html:
        return 'For web browsers';
      case MediaFileType.txt:
        return 'Plain text format';
      case MediaFileType.audio:
        return 'Download audio file';
      case MediaFileType.video:
        return 'Download video file';
    }
  }
}
