import 'package:freezed_annotation/freezed_annotation.dart';

part 'media_metadata.freezed.dart';
part 'media_metadata.g.dart';

/// A structured class to replace the untyped Map<String, dynamic>? metadata
@freezed
abstract class MediaMetadata with _$MediaMetadata {
  // Add this line to include methods in the generated class

  const factory MediaMetadata({
    // Document-related metadata
    String? description,

    // HTML content
    String? html,

    // Social media metrics
    int? likeCount,
    int? retweetCount,
    int? commentCount,
    int? shareCount,

    // Video-specific metadata
    int? viewCount,
    String? resolution,
    String? codec,

    // Audio-specific metadata
    String? artist,
    String? album,
    String? genre,
    int? bitrate,

    // Publication metadata
    String? author,
    String? publisher,
    DateTime? publishDate,
    List<String>? tags,

    // Additional fields that don't fit the categories above
    Map<String, dynamic>? additionalInfo,
  }) = _MediaMetadata;
  const MediaMetadata._();

  factory MediaMetadata.fromJson(Map<String, dynamic> json) =>
      _$MediaMetadataFromJson(json);

  /// Factory method to convert from the old untyped Map<String, dynamic>
  factory MediaMetadata.fromMap(Map<String, dynamic>? map) {
    if (map == null) {
      return const MediaMetadata();
    }

    // Extract fields
    return MediaMetadata(
      description: map['description'] as String?,
      html: map['html'] as String?,
      likeCount: map['likeCount'] as int?,
      retweetCount: map['retweetCount'] as int?,
      commentCount: map['commentCount'] as int?,
      shareCount: map['shareCount'] as int?,
      viewCount: map['viewCount'] as int?,
      resolution: map['resolution'] as String?,
      codec: map['codec'] as String?,
      artist: map['artist'] as String?,
      album: map['album'] as String?,
      genre: map['genre'] as String?,
      bitrate: map['bitrate'] as int?,
      author: map['author'] as String?,
      publisher: map['publisher'] as String?,
      publishDate: map['publishDate'] != null
          ? DateTime.parse(map['publishDate'] as String)
          : null,
      tags: map['tags'] != null
          ? List<String>.from(map['tags'] as List<dynamic>)
          : null,

      // Store any additional fields that weren't explicitly mapped
      additionalInfo: Map<String, dynamic>.from(map)
        ..removeWhere((String key, _) => <String>[
              'documentUrls',
              'description',
              'html',
              'likeCount',
              'retweetCount',
              'commentCount',
              'shareCount',
              'viewCount',
              'resolution',
              'codec',
              'artist',
              'album',
              'genre',
              'bitrate',
              'author',
              'publisher',
              'publishDate',
              'tags'
            ].contains(key)),
    );
  }

  /// Convert to a Map<String, dynamic> for backward compatibility
  Map<String, dynamic> toMap() {
    final Map<String, dynamic> result = <String, dynamic>{};

    // Add all non-null fields
    if (description != null) result['description'] = description;
    if (html != null) result['html'] = html;
    if (likeCount != null) result['likeCount'] = likeCount;
    if (retweetCount != null) result['retweetCount'] = retweetCount;
    if (commentCount != null) result['commentCount'] = commentCount;
    if (shareCount != null) result['shareCount'] = shareCount;
    if (viewCount != null) result['viewCount'] = viewCount;
    if (resolution != null) result['resolution'] = resolution;
    if (codec != null) result['codec'] = codec;
    if (artist != null) result['artist'] = artist;
    if (album != null) result['album'] = album;
    if (genre != null) result['genre'] = genre;
    if (bitrate != null) result['bitrate'] = bitrate;
    if (author != null) result['author'] = author;
    if (publisher != null) result['publisher'] = publisher;
    if (publishDate != null)
      result['publishDate'] = publishDate!.toIso8601String();
    if (tags != null) result['tags'] = tags;

    // Add any additional fields
    if (additionalInfo != null) {
      result.addAll(additionalInfo!);
    }

    return result;
  }
}
