import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'navigation_provider.g.dart';

/// A provider for navigation functions
@riverpod
NavigationService navigation(Ref ref) {
  return NavigationService();
}

/// Service class for handling navigation
class NavigationService {
  /// Safely navigate back or to home if there's nothing to pop
  void safeGoBack(BuildContext context) {
    try {
      if (Navigator.of(context).canPop()) {
        context.pop();
      } else {
        // Navigate to home if we can't pop
        context.go('/home');
      }
    } catch (e) {
      // If any error occurs, navigate to home
      debugPrint('Error navigating back: $e');
      context.go('/home');
    }
  }
}
