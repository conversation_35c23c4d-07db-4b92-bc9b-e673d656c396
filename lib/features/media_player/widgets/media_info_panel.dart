import 'package:flutter/material.dart';

import '../../../data/models/media_ui_model.dart';

class MediaInfoPanel extends StatelessWidget {
  const MediaInfoPanel({
    required this.mediaItem,
    super.key,
  });

  final MediaUiModel mediaItem;

  @override
  Widget build(BuildContext context) {
    try {
      return Container(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                mediaItem.title,
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              _buildInfoRow(
                context,
                'Type',
                mediaItem.type.toUpperCase(),
                Icons.category,
              ),
              _buildInfoRow(
                context,
                'Category',
                mediaItem.category,
                Icons.folder,
              ),
              if (mediaItem.audioUrl != null && mediaItem.audioUrl!.isNotEmpty)
                _buildInfoRow(
                  context,
                  'Source',
                  _formatUrl(mediaItem.audioUrl!),
                  Icons.link,
                ),
            ],
          ),
        ),
      );
    } catch (e) {
      // If there's an error rendering the panel, show a fallback UI
      return Container(
        padding: const EdgeInsets.all(16.0),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              const Icon(Icons.error_outline, color: Colors.red),
              const SizedBox(height: 8),
              Text(
                'Error displaying media information: $e',
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget _buildInfoRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Icon(
            icon,
            size: 20,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                ),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatUrl(String url) {
    // Truncate long URLs for display
    if (url.length > 40) {
      return '${url.substring(0, 20)}...${url.substring(url.length - 20)}';
    }
    return url;
  }
}
