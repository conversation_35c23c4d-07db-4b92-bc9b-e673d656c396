/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

class $AssetsFontsGen {
  const $AssetsFontsGen();

  /// File path: assets/fonts/Cairo-Black.ttf
  String get cairoBlack => 'assets/fonts/Cairo-Black.ttf';

  /// File path: assets/fonts/Cairo-Bold.ttf
  String get cairoBold => 'assets/fonts/Cairo-Bold.ttf';

  /// File path: assets/fonts/Cairo-ExtraBold.ttf
  String get cairoExtraBold => 'assets/fonts/Cairo-ExtraBold.ttf';

  /// File path: assets/fonts/Cairo-ExtraLight.ttf
  String get cairoExtraLight => 'assets/fonts/Cairo-ExtraLight.ttf';

  /// File path: assets/fonts/Cairo-Light.ttf
  String get cairoLight => 'assets/fonts/Cairo-Light.ttf';

  /// File path: assets/fonts/Cairo-Medium.ttf
  String get cairoMedium => 'assets/fonts/Cairo-Medium.ttf';

  /// File path: assets/fonts/Cairo-Regular.ttf
  String get cairoRegular => 'assets/fonts/Cairo-Regular.ttf';

  /// File path: assets/fonts/Cairo-SemiBold.ttf
  String get cairoSemiBold => 'assets/fonts/Cairo-SemiBold.ttf';

  /// File path: assets/fonts/Nunito-Bold.ttf
  String get nunitoBold => 'assets/fonts/Nunito-Bold.ttf';

  /// File path: assets/fonts/Nunito-Light.ttf
  String get nunitoLight => 'assets/fonts/Nunito-Light.ttf';

  /// File path: assets/fonts/Nunito-Medium.ttf
  String get nunitoMedium => 'assets/fonts/Nunito-Medium.ttf';

  /// File path: assets/fonts/Nunito-Regular.ttf
  String get nunitoRegular => 'assets/fonts/Nunito-Regular.ttf';

  /// List of all assets
  List<String> get values => [
    cairoBlack,
    cairoBold,
    cairoExtraBold,
    cairoExtraLight,
    cairoLight,
    cairoMedium,
    cairoRegular,
    cairoSemiBold,
    nunitoBold,
    nunitoLight,
    nunitoMedium,
    nunitoRegular,
  ];
}

class $AssetsJsonsGen {
  const $AssetsJsonsGen();

  /// File path: assets/jsons/samples.json
  String get samples => 'assets/jsons/samples.json';

  /// List of all assets
  List<String> get values => [samples];
}

class $AssetsTranslationsGen {
  const $AssetsTranslationsGen();

  /// File path: assets/translations/ar.json
  String get ar => 'assets/translations/ar.json';

  /// File path: assets/translations/en-US.json
  String get enUS => 'assets/translations/en-US.json';

  /// File path: assets/translations/en.json
  String get en => 'assets/translations/en.json';

  /// List of all assets
  List<String> get values => [ar, enUS, en];
}

class Assets {
  const Assets._();

  static const $AssetsFontsGen fonts = $AssetsFontsGen();
  static const $AssetsJsonsGen jsons = $AssetsJsonsGen();
  static const $AssetsTranslationsGen translations = $AssetsTranslationsGen();
}
