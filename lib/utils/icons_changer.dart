import 'package:flutter/material.dart';

IconData getIconForType(String type) {
  switch (type.toLowerCase()) {
    case 'video':
      return Icons.video_library;
    case 'audio':
      return Icons.audiotrack;
    case 'pdf':
    case 'document':
      return Icons.insert_drive_file;
    case 'text':
      return Icons.article;
    case 'tweet':
      return Icons.chat;
    default:
      return Icons.insert_drive_file;
  }
}

IconData getIconForCategory(String category) {
  switch (category.toLowerCase()) {
    case 'lessons':
      return Icons.school;
    case 'sermons':
      return Icons.record_voice_over;
    case 'lectures':
      return Icons.mic;
    case 'radio':
      return Icons.radio;
    case 'books':
      return Icons.book;
    case 'articles':
      return Icons.article;
    case 'photos':
      return Icons.photo_library;
    case 'videos':
      return Icons.video_library;
    default:
      return Icons.folder;
  }
}
