import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'search_provider.g.dart';

/// State class for the search screen
class SearchState {
  const SearchState({
    this.searchQuery = '',
    this.selectedType,
  });
  final String searchQuery;
  final String? selectedType;

  /// Creates a copy of this state with the given fields replaced
  SearchState copyWith({
    String? searchQuery,
    String? selectedType,
  }) {
    return SearchState(
      searchQuery: searchQuery ?? this.searchQuery,
      selectedType: selectedType ?? this.selectedType,
    );
  }
}

/// Provider for the search state
@riverpod
class SearchNotifier extends _$SearchNotifier {
  @override
  SearchState build() {
    return const SearchState();
  }

  /// Updates the search query
  void updateSearchQuery(String query) {
    state = state.copyWith(searchQuery: query);
  }

  /// Sets the selected media type filter
  void setSelectedType(String? type) {
    state = state.copyWith(selectedType: type);
  }

  /// Resets the search state
  void reset() {
    state = const SearchState();
  }
}
